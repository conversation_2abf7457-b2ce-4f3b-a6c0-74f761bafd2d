-- Organizations table
CREATE TABLE organizations (
    org_id UUID PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    created_by UUID NOT NULL REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Organization members table
CREATE TABLE org_members (
    org_id UUID NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    PRIMARY KEY (org_id, user_id)
);

-- Organization roles table
CREATE TABLE org_roles (
    role_id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE
);

-- Insert initial roles
INSERT INTO org_roles (name)
VALUES
('ROLE_ORG_ADMIN'),
('ROLE_ORG_HR'),
('ROLE_ORG_INTERVIEWER');

-- Organization role mapping table
CREATE TABLE org_role_mapping (
    org_id      UUID NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
    user_id     UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    role_id BIGSERIAL NOT NULL REFERENCES org_roles(role_id) ON DELETE CASCADE,
    PRIMARY KEY (org_id, user_id)
);

-- Organizations Permissions table
CREATE TABLE org_permissions (
    permission_id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) NOT NULL
);

-- Insert initial permissions
INSERT INTO org_permissions (name, description, category)
VALUES
('MANAGE_ORG', 'Manage organization settings', 'ORG_SETTINGS'),
('MANAGE_JOB', 'Manage jobs', 'JOB');


CREATE TABLE org_role_permissions (
    role_id BIGINT NOT NULL REFERENCES org_roles(role_id) ON DELETE CASCADE,
    permission_id BIGINT NOT NULL REFERENCES org_permissions(permission_id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permission_id)
);

-- Map permissions to roles
-- ORG_ADMIN gets all permissions
INSERT INTO org_role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM org_roles r,
     org_permissions p
WHERE r.name = 'ROLE_ORG_ADMIN';


-- ORG_HR gets "JOB" permissions (for now)
INSERT INTO org_role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM org_roles r,
     org_permissions p
WHERE r.name = 'ROLE_ORG_HR'
  AND p.category = 'JOB';

-- ORG_INTERVIEWER gets "JOB" permissions (for now)
INSERT INTO org_role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM org_roles r,
     org_permissions p
WHERE r.name = 'ROLE_ORG_INTERVIEWER'
  AND p.category = 'JOB';
