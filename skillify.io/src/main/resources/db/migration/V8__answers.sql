-- Answers table
CREATE TABLE answers (
    test_id UUID NOT NULL REFERENCES tests(test_id) ON DELETE CASCADE,
    question_id UUID NOT NULL REFERENCES questions(question_id) ON DELETE CASCADE,
    candidate_id UUID NOT NULL REFERENCES users(user_id),
    answer JSONB NOT NULL,
    is_correct BOOLEAN,
    score FLOAT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    version BIGINT NOT NULL DEFAULT 0,
    PRIMARY KEY (test_id, question_id, candidate_id)
);

CREATE INDEX idx_answer_test_id ON answers(test_id);
CREATE INDEX idx_answer_question_id ON answers(question_id);
CREATE INDEX idx_answer_candidate_id ON answers(candidate_id);
CREATE INDEX idx_answer_is_correct ON answers(is_correct);
