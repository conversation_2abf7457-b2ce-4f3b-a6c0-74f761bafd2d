-- Test assignments table
CREATE TABLE test_assignments (
    assignment_id UUID PRIMARY KEY,
    test_id UUID NOT NULL REFERENCES tests(test_id) ON DELETE CASCADE,
    candidate_email VARCHAR(255) NOT NULL,
    status VARCHAR(30) NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    score FLOAT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by UUID NOT NULL REFERENCES users(user_id),
    updated_by UUID NOT NULL REFERENCES users(user_id),
    version BIGINT NOT NULL DEFAULT 0,
    CONSTRAINT uq_test_candidate_email UNIQUE (test_id, candidate_email)
);

CREATE INDEX idx_assignment_test_id ON test_assignments(test_id);
CREATE INDEX idx_assignment_candidate_email ON test_assignments(candidate_email);
CREATE INDEX idx_assignment_status ON test_assignments(status);
