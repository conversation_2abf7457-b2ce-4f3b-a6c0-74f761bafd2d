CREATE TABLE jobs (
    job_id UUID PRIMARY KEY,
    title VARCHAR(255) NOT NULL CHECK (LENGTH(title) >= 3),
    description TEXT CHECK (LENGTH(description) <= 5000),
    organization_id UUID NOT NULL REFERENCES organizations(org_id) ON DELETE CASCADE,
    is_active BOOLEAN NOT NULL DEFAULT true,
    version BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID NOT NULL REFERENCES users(user_id),
    updated_by UUID NOT NULL REFERENCES users(user_id)
);

CREATE INDEX idx_job_org_id ON jobs(organization_id);
CREATE INDEX idx_job_title ON jobs(title);
CREATE INDEX idx_job_is_active ON jobs(is_active);
