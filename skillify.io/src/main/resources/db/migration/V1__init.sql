CREATE EXTENSION IF NOT EXISTS citext;

-- User table
CREATE TABLE users
(
    user_id                         UUID PRIMARY KEY,
    email                           CITEXT                   NOT NULL,
    username                        CITEXT                   NOT NULL,
    password                        VARCHAR(128)             NOT NULL,
    verified                        <PERSON><PERSON><PERSON><PERSON><PERSON>                  NOT NULL DEFAULT FALSE,
    last_login                      TIMESTAMP WITH TIME ZONE,
    created_at                      TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                      TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    password_reset_token            VARCHAR(128),
    password_reset_token_expiry     TIMESTAMP WITH TIME ZONE,
    email_verification_token        VARCHAR(128),
    email_verification_token_expiry TIMESTAMP WITH TIME ZONE,
    CONSTRAINT uk_users_email UNIQUE (email),
    CONSTRAINT uk_users_username UNIQUE (username)
);

CREATE INDEX idx_users_created_at ON users (created_at);
CREATE INDEX idx_users_email_verification_token ON users (email_verification_token);

-- Roles table
CREATE TABLE roles
(
    role_id BIGSERIAL PRIMARY KEY,
    name    VARCHAR(50) NOT NULL UNIQUE
);

CREATE TABLE user_roles
(
    user_id UUID   NOT NULL REFERENCES users (user_id) ON DELETE CASCADE,
    role_id BIGINT NOT NULL REFERENCES roles (role_id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, role_id)
);

CREATE INDEX idx_user_roles_user ON user_roles (user_id);
CREATE INDEX idx_user_roles_role ON user_roles (role_id);

-- Initial data
INSERT INTO roles (name)
VALUES ('ROLE_USER'),
       ('ROLE_ADMIN');
