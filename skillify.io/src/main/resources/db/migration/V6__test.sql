-- Add new permissions for test management
INSERT INTO org_permissions (name, description, category)
VALUES
('MANAGE_TEST', 'Create, update, and delete tests', 'TEST');

-- Map new permissions
INSERT INTO org_role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM org_roles r, org_permissions p
WHERE r.name IN ('ROLE_ORG_ADMIN', 'ROLE_ORG_HR', 'ROLE_ORG_INTERVIEWER')
  AND p.category = 'TEST';

-- Create test table
CREATE TABLE tests (
    test_id UUID PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES jobs(job_id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    time_limit INTEGER,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by UUID NOT NULL REFERENCES users(user_id),
    updated_by UUID NOT NULL REFERENCES users(user_id),
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) NOT NULL,
    version BIGINT NOT NULL DEFAULT 0
);

-- Indexes for test table
CREATE INDEX idx_test_job_id ON tests(job_id);
CREATE INDEX idx_test_status ON tests(status);
CREATE INDEX idx_test_name ON tests(name);
