spring.application.name=Skillify
spring.profiles.active=dev
spring.config.import=optional:file:.env[.properties]

# JPA/Hibernate
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.open-in-view=false

# Logging Configuration
logging.level.root=INFO
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO
logging.level.io.skillify=DEBUG

# Flyway
spring.flyway.enabled=true
spring.flyway.validate-on-migrate=true
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration

# OpenAPI/Swagger Configuration
# Raw OpenAPI JSON
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
# Sort operations by HTTP method
springdoc.swagger-ui.operationsSorter=method
# Sort API groups alphabetically
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.try-it-out-enabled=true
# Show search/filter box
springdoc.swagger-ui.filter=true
# Keep endpoints collapsed by default
springdoc.swagger-ui.doc-expansion=none
springdoc.swagger-ui.syntaxHighlight.theme=monokai
