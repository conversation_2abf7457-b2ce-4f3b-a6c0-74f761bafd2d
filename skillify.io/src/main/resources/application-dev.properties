server.port=8080
spring.datasource.url=jdbc:postgresql://${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
spring.datasource.username=${POSTGRES_USER}
spring.datasource.password=${POSTGRES_PASSWORD}
jwt.secret=${JWT_SECRET}

# Logging
logging.level.org.springframework.transaction=DEBUG
logging.level.org.hibernate.engine.transaction=TRACE
logging.level.root=INFO

# Spring jpa properties
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
