package io.skillify.controllers.role;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.role.PermissionDto;
import io.skillify.dtos.role.RoleDto;
import io.skillify.services.role.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@Validated
@Tag(name = "Roles & Permissions (Admin)", description = "Admin API for managing roles and permissions")
public class RoleController {

    private final RoleService roleService;

    @PostMapping("/api/admin/roles")
    @PreAuthorize("hasAuthority('ROLE_MANAGE')")
    @Operation(summary = "Create a new role", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Role created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or role already exists", content = @Content),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content)
    })
    public ResponseEntity<ResponseDto<RoleDto.RoleResponseDto>> createRole(@Valid @RequestBody RoleDto.RoleCreateRequestDto createRequestDto) {
        RoleDto.RoleResponseDto createdRole = roleService.createRole(createRequestDto);
        return new ResponseEntity<>(ResponseDto.success(createdRole), HttpStatus.CREATED);
    }

    @GetMapping("/api/admin/roles")
    @PreAuthorize("hasAuthority('ROLE_MANAGE')")
    @Operation(summary = "Get all roles", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of roles retrieved"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content)
    })
    public ResponseEntity<ResponseDto<List<RoleDto.RoleResponseDto>>> getAllRoles() {
        List<RoleDto.RoleResponseDto> roles = roleService.getAllRoles();
        return new ResponseEntity<>(ResponseDto.success(roles), HttpStatus.OK);
    }

    @GetMapping("/api/admin/roles/{roleId}")
    @PreAuthorize("hasAuthority('ROLE_MANAGE')")
    @Operation(summary = "Get role by ID", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role details retrieved"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "Role not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<RoleDto.RoleResponseDto>> getRoleById(
            @Parameter(description = "ID of the role to retrieve") @PathVariable Long roleId) {
        RoleDto.RoleResponseDto role = roleService.getRoleById(roleId);
        return new ResponseEntity<>(ResponseDto.success(role), HttpStatus.OK);
    }

    @GetMapping("/api/admin/roles/name/{roleName}")
    @PreAuthorize("hasAuthority('ROLE_MANAGE')")
    @Operation(summary = "Get role by name", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role details retrieved"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "Role not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<RoleDto.RoleResponseDto>> getRoleByName(
            @Parameter(description = "Name of the role to retrieve (e.g., ROLE_USER)") @PathVariable String roleName) {
        RoleDto.RoleResponseDto role = roleService.getRoleByName(roleName);
        return new ResponseEntity<>(ResponseDto.success(role), HttpStatus.OK);
    }

    @PutMapping("/api/admin/roles/{roleId}")
    @PreAuthorize("hasAuthority('ROLE_MANAGE')")
    @Operation(summary = "Update an existing role", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or role name conflict", content = @Content),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "Role not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<RoleDto.RoleResponseDto>> updateRole(
            @Parameter(description = "ID of the role to update") @PathVariable Long roleId,
            @Valid @RequestBody RoleDto.RoleUpdateRequestDto updateRequestDto) {
        RoleDto.RoleResponseDto updatedRole = roleService.updateRole(roleId, updateRequestDto);
        return new ResponseEntity<>(ResponseDto.success(updatedRole), HttpStatus.OK);
    }

    @DeleteMapping("/api/admin/roles/{roleId}")
    @PreAuthorize("hasAuthority('ROLE_MANAGE')")
    @Operation(summary = "Delete a role", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role deleted successfully (returns no content if void)"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "Role not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<Object>> deleteRole(
            @Parameter(description = "ID of the role to delete") @PathVariable Long roleId) {
        roleService.deleteRole(roleId);
        return new ResponseEntity<>(ResponseDto.success(null), HttpStatus.OK);
    }

    @GetMapping("/api/admin/permissions")
    @PreAuthorize("hasAuthority('ROLE_MANAGE')")
    @Operation(summary = "Get all available permissions", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of all permissions retrieved"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content)
    })
    public ResponseEntity<ResponseDto<List<PermissionDto>>> getAllPermissions() {
        List<PermissionDto> permissions = roleService.getAllPermissions();
        return new ResponseEntity<>(ResponseDto.success(permissions), HttpStatus.OK);
    }
}
