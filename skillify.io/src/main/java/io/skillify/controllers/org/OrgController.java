package io.skillify.controllers.org;

import java.util.List;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.org.OrgDto;
import io.skillify.dtos.org.OrgDto.DetailedResponse;
import io.skillify.dtos.test.TestDto;
import io.skillify.services.org.OrgService;
import io.skillify.services.test.TestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;


@RestController
@RequiredArgsConstructor
@Validated
@Tag(name = "Organizations", description = "Organization management API")
public class OrgController {
    private final OrgService orgService;
    private final TestService testService;

    @PostMapping("/api/organizations")
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(
        summary = "Create a new organization",
        description = "Creates a new organization with the current user as owner",
        security = @SecurityRequirement(name = "bearerAuth")
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Organization created"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    public ResponseEntity<ResponseDto<OrgDto.Response>> createOrganization(
            @Parameter(hidden = true) Authentication authentication,
            @RequestBody @Validated OrgDto.CreateRequest createRequest) {
        OrgDto.Response org = orgService.createOrganization(createRequest, authentication);
        return ResponseEntity.status(HttpStatus.CREATED).body(ResponseDto.success(org));
    }

    @GetMapping("/api/organizations/{organizationId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermission(#organizationId, 'MANAGE_ORG')")
    @Operation(
        summary = "Get organization details",
        description = "Retrieves detailed information about an organization",
        security = @SecurityRequirement(name = "bearerAuth")
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organization details"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Organization not found")
    })
    public ResponseEntity<ResponseDto<?>> getOrganization(
            @Parameter(description = "Organization ID", required = true) @PathVariable UUID organizationId) {
        DetailedResponse org = orgService.getOrganizationDetails(organizationId);
        return ResponseEntity.ok(ResponseDto.success(org));
    }

    @PatchMapping("/api/organizations/{organizationId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermission(#organizationId, 'MANAGE_ORG')")
    @Operation(summary = "Update an organization", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organization updated"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Organization not found")
    })
    public ResponseEntity<ResponseDto<OrgDto.DetailedResponse>> updateOrganization(
            @Parameter(description = "Organization ID", required = true) @PathVariable UUID organizationId,
            @RequestBody @Validated OrgDto.UpdateRequest updateRequest) {
        OrgDto.DetailedResponse updated = orgService.updateOrganization(organizationId, updateRequest);
        return ResponseEntity.ok(ResponseDto.success(updated));
    }

    @DeleteMapping("/api/organizations/{organizationId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermission(#organizationId, 'MANAGE_ORG')")
    @Operation(summary = "Delete an organization", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organization deleted"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Organization not found")
    })
    public ResponseEntity<?> deleteOrganization(
            @Parameter(description = "Organization ID", required = true) @PathVariable UUID organizationId) {
        return ResponseEntity.ok(orgService.deleteOrganization(organizationId));
    }

    @GetMapping("/api/organizations/user/current")
    @Operation(
        summary = "Get current user's organizations",
        description = "Retrieves all organizations associated with the current user",
        security = @SecurityRequirement(name = "bearerAuth")
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of organizations"),
            @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    public ResponseEntity<ResponseDto<List<OrgDto.Response>>> getUserOrganizations(
            @Parameter(hidden = true) Authentication authentication) {
        List<OrgDto.Response> orgs = orgService.getUserOrganizations(authentication);
        return ResponseEntity.ok(ResponseDto.success(orgs));
    }

    @PostMapping("/api/organizations/{organizationId}/members")
    @PreAuthorize("@orgPermissionEvaluator.hasPermission(#organizationId, 'MANAGE_ORG')")
    @Operation(summary = "Add a member to an organization", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Member added"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Organization or user not found")
    })
    public ResponseEntity<?> addMember(
            @Parameter(description = "Organization ID", required = true) @PathVariable UUID organizationId,
            @RequestBody @Validated OrgDto.AddMemberRequest request) {
        return ResponseEntity.ok(orgService.addMember(organizationId, request));
    }

    @GetMapping("/api/organizations/{organizationId}/tests")
    @PreAuthorize("@orgPermissionEvaluator.hasPermission(#organizationId, 'MANAGE_ORG')")
    @Operation(
        summary = "Get all tests of an organization (paginated)",
        description = "Retrieves a paginated list of tests associated with the organization",
        security = @SecurityRequirement(name = "bearerAuth")
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Paginated list of tests"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Organization not found")
    })
    public ResponseEntity<ResponseDto<Page<TestDto.Response>>> getAllTests(
            @Parameter(description = "Organization ID", required = true) @PathVariable UUID organizationId,
            @Parameter(description = "Pagination information") @PageableDefault(size = 20) Pageable pageable) {
        Page<TestDto.Response> tests = testService.getTestsByOrganizationId(organizationId, pageable);
        return ResponseEntity.ok(ResponseDto.success(tests));
    }
}
