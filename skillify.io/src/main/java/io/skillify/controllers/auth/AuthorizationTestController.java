package io.skillify.controllers.auth;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test")
public class AuthorizationTestController {

    @GetMapping("/open")
    public String openEndpoint() {
        return "This endpoint is available for authenticated users.";
    }

    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @GetMapping("/admin")
    public String adminEndpoint() {
        return "Hello Admin! You have access to admin resources.";
    }

    @PreAuthorize("permitAll()")
    @GetMapping("/public")
    public String permitAllEndpoint() {
        return "This endpoint is available for everyone, even unauthenticated users.";
    }

}
