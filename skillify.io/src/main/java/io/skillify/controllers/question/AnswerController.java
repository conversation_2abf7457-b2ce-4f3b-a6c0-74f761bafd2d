package io.skillify.controllers.question;

import java.util.List;
import java.util.UUID;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.question.AnswerDto;
import io.skillify.dtos.question.AnswerDto.BatchSubmissionResponse;
import io.skillify.dtos.question.AnswerDto.BatchSubmitAnswerRequest;
import io.skillify.dtos.question.AnswerDto.SubmitAnswerRequest;
import io.skillify.models.question.Answer.AnswerId;
import io.skillify.services.question.AnswerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@Tag(name = "Answers", description = "API for managing answers")
public class AnswerController {

    private final AnswerService answerService;

    @PostMapping("/api/answers")
    @PreAuthorize("@orgPermissionEvaluator.canTakeTest(#request.testId)")
    @Operation(summary = "Submit an answer", description = "Submits an answer for a question")
    public ResponseEntity<ResponseDto<AnswerDto.Response>> submitAnswer(
            @Valid @RequestBody SubmitAnswerRequest request) {
        AnswerDto.Response submittedAnswer = answerService.submitAnswer(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(submittedAnswer));
    }

    @PostMapping("/api/answers/batch")
    @PreAuthorize("@orgPermissionEvaluator.canTakeTest(#request.testId)")
    @Operation(summary = "Submit multiple answers",
              description = "Submits multiple answers for a test")
    public ResponseEntity<ResponseDto<BatchSubmissionResponse>> submitBatchAnswers(
            @Valid @RequestBody BatchSubmitAnswerRequest request) {
        BatchSubmissionResponse response = answerService.submitBatchAnswers(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(response));
    }

    @Operation(
        summary = "Get answers for a test and candidate, with optional metrics",
        description = "Retrieves answers for a specific test and candidate. Can also retrieve metrics like average-score, correct-count, total-count.",
        security = @SecurityRequirement(name = "JWT")
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Answers or metrics retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Test or Candidate not found")
    })
    @GetMapping("/api/tests/{testId}/candidates/{candidateId}/answers")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<?>> getAnswersForTestCandidate(
            @Parameter(description = "ID of the test", required = true) @PathVariable UUID testId,
            @Parameter(description = "ID of the candidate", required = true) @PathVariable UUID candidateId,
            @Parameter(description = "Optional metric (average-score, correct-count, total-count)") @RequestParam(required = false) String metric) {

        if (metric != null) {
            switch (metric.toLowerCase()) {
                case "average-score":
                    Float averageScore = answerService.calculateAverageScoreByTestAndCandidate(testId, candidateId);
                    return ResponseEntity.ok(ResponseDto.success(averageScore));
                case "correct-count":
                    Long correctCount = answerService.countCorrectAnswersByTestAndCandidate(testId, candidateId);
                    return ResponseEntity.ok(ResponseDto.success(correctCount));
                case "total-count":
                    Long totalCount = answerService.countTotalAnswersByTestAndCandidate(testId, candidateId);
                    return ResponseEntity.ok(ResponseDto.success(totalCount));
                default:
                    return ResponseEntity
                            .status(HttpStatus.BAD_REQUEST)
                            .body(ResponseDto.error("Invalid metric: " + metric, HttpStatus.BAD_REQUEST));
            }
        } else {
            List<AnswerDto.Response> answers = answerService.getAnswersByTestAndCandidate(testId, candidateId);
            return ResponseEntity.ok(ResponseDto.success(answers));
        }
    }

    @Operation(
        summary = "Get a specific answer by test, question, and candidate",
        description = "Retrieves a specific answer by its composite key (testId, questionId, candidateId).",
        security = @SecurityRequirement(name = "JWT")
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Answer retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Answer not found")
    })
    @GetMapping("/api/tests/{testId}/questions/{questionId}/candidates/{candidateId}/answers")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<AnswerDto.Response>> getSpecificAnswerForTestQuestionCandidate(
            @Parameter(description = "ID of the test", required = true) @PathVariable UUID testId,
            @Parameter(description = "ID of the question", required = true) @PathVariable UUID questionId,
            @Parameter(description = "ID of the candidate", required = true) @PathVariable UUID candidateId) {
        AnswerDto.Response answer = answerService.getAnswer(new AnswerId(testId, questionId, candidateId));
        return ResponseEntity.ok(ResponseDto.success(answer));
    }

    @Operation(
        summary = "Delete a specific answer by test, question, and candidate",
        description = "Deletes a specific answer by its composite key (testId, questionId, candidateId).",
        security = @SecurityRequirement(name = "JWT")
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Answer deleted successfully"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Answer not found")
    })
    @DeleteMapping("/api/tests/{testId}/questions/{questionId}/candidates/{candidateId}/answers")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<Void>> deleteSpecificAnswerForTestQuestionCandidate(
            @Parameter(description = "ID of the test", required = true) @PathVariable UUID testId,
            @Parameter(description = "ID of the question", required = true) @PathVariable UUID questionId,
            @Parameter(description = "ID of the candidate", required = true) @PathVariable UUID candidateId) {
        answerService.deleteAnswer(new AnswerId(testId, questionId, candidateId));
        return ResponseEntity.ok(ResponseDto.success(null));
    }
}
