package io.skillify.controllers.test;

import java.util.List;
import java.util.UUID;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.test.TestAssignmentDto;
import io.skillify.dtos.test.TestAssignmentDto.CreateTestAssignmentRequest;
import io.skillify.dtos.test.TestAssignmentDto.UpdateTestAssignmentRequest;
import io.skillify.models.test.TestAssignmentStatus;
import io.skillify.services.TestAssignment.TestAssignmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@Tag(name = "Test Assignments", description = "API for managing test assignments")
public class TestAssignmentController {

    private final TestAssignmentService testAssignmentService;

    @PostMapping("/api/test-assignments")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#request.testId, 'MANAGE_TEST')")
    @Operation(summary = "Create batch of test assignments", description = "Creates multiple test assignments for a single test using candidate emails. Provide testId once and a list of assignment details (email).")
    public ResponseEntity<ResponseDto<List<TestAssignmentDto.Response>>> createTestAssignments(
            @Valid @RequestBody CreateTestAssignmentRequest request) {
        List<TestAssignmentDto.Response> createdAssignments = testAssignmentService.createTestAssignments(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(createdAssignments));
    }

    @GetMapping("/api/test-assignments/{assignmentId}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @orgPermissionEvaluator.hasPermissionForTestAssignment(#assignmentId, 'MANAGE_TEST')")
    @Operation(summary = "Get a test assignment by ID", description = "Retrieves a test assignment by its ID")
    public ResponseEntity<ResponseDto<TestAssignmentDto.Response>> getTestAssignment(@PathVariable UUID assignmentId) {
        TestAssignmentDto.Response assignment = testAssignmentService.getTestAssignment(assignmentId);
        return ResponseEntity.ok(ResponseDto.success(assignment));
    }

    @GetMapping(value = "/api/test-assignments", params = "status")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @Operation(summary = "Get all test assignments by status", description = "Retrieves all test assignments with a specific status. Requires ?status=... query parameter.")
    public ResponseEntity<ResponseDto<List<TestAssignmentDto.Response>>> getTestAssignmentsByStatus(
            @RequestParam TestAssignmentStatus status) {
        List<TestAssignmentDto.Response> assignments = testAssignmentService.getTestAssignmentsByStatus(status);
        return ResponseEntity.ok(ResponseDto.success(assignments));
    }

    @PatchMapping("/api/test-assignments/{assignmentId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTestAssignment(#assignmentId, 'MANAGE_TEST')")
    @Operation(summary = "Update a test assignment", description = "Updates an existing test assignment (e.g., status). Candidate email cannot be changed here.")
    public ResponseEntity<ResponseDto<TestAssignmentDto.Response>> updateTestAssignment(
            @PathVariable UUID assignmentId,
            @Valid @RequestBody UpdateTestAssignmentRequest request) {
        TestAssignmentDto.Response updatedAssignment = testAssignmentService.updateTestAssignment(assignmentId, request);
        return ResponseEntity.ok(ResponseDto.success(updatedAssignment));
    }

    @DeleteMapping("/api/test-assignments/{assignmentId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTestAssignment(#assignmentId, 'MANAGE_TEST')")
    @Operation(summary = "Delete a test assignment", description = "Deletes a test assignment by its ID")
    public ResponseEntity<ResponseDto<Void>> deleteTestAssignment(@PathVariable UUID assignmentId) {
        testAssignmentService.deleteTestAssignment(assignmentId);
        return ResponseEntity.ok(ResponseDto.success(null));
    }

    @Operation(
        summary = "Get test assignments for a specific test",
        description = "Retrieves test assignments for a specific test. Can be filtered by status and can retrieve a count."
    )
    @GetMapping("/api/tests/{testId}/test-assignments")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<?>> getAssignmentsForSpecificTest(
            @Parameter(description = "ID of the test", required = true) @PathVariable UUID testId,
            @Parameter(description = "Optional filter by assignment status") @RequestParam(required = false) TestAssignmentStatus status,
            @Parameter(description = "Optional. Set to 'count' to get the number of assignments.") @RequestParam(required = false) String metric) {

        if ("count".equalsIgnoreCase(metric)) {
            long count;
            if (status != null) {
                count = testAssignmentService.countTestAssignmentsByTestAndStatus(testId, status);
            } else {
                count = testAssignmentService.countTestAssignmentsByTest(testId);
            }
            return ResponseEntity.ok(ResponseDto.success(count));
        } else {
            List<TestAssignmentDto.Response> assignments;
            if (status != null) {
                assignments = testAssignmentService.getTestAssignmentsByTestAndStatus(testId, status);
            } else {
                assignments = testAssignmentService.getTestAssignmentsByTest(testId);
            }
            return ResponseEntity.ok(ResponseDto.success(assignments));
        }
    }
}
