package io.skillify.services.user;

import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import io.skillify.dtos.user.UserDto;

public interface UserService {

    UserDto.UserResponseDto getCurrentUserDetails();

    UserDto.UserResponseDto updateUserProfile(UUID userId, UserDto.UserProfileUpdateRequestDto requestDto);

    void changeUserPassword(UUID userId, UserDto.PasswordChangeRequestDto requestDto);

    // Admin operations
    UserDto.UserResponseDto adminCreateUser(UserDto.UserCreateRequestDto createRequestDto);

    Page<UserDto.UserResponseDto> getAllUsers(Pageable pageable);

    UserDto.UserResponseDto getUserById(UUID userId);

    UserDto.UserResponseDto getUserByUsername(String username);

    UserDto.UserResponseDto getUserByEmail(String email);

    UserDto.UserResponseDto adminUpdateUser(UUID userId, UserDto.AdminUserUpdateRequestDto updateRequestDto);

    void adminDeleteUser(UUID userId);

    void adminResetUserPassword(UUID userId, UserDto.AdminPasswordResetRequestDto requestDto);
}
