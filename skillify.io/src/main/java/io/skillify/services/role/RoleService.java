package io.skillify.services.role;

import io.skillify.dtos.role.PermissionDto;
import io.skillify.dtos.role.RoleDto;

import java.util.List;

public interface RoleService {

    RoleDto.RoleResponseDto createRole(RoleDto.RoleCreateRequestDto createRequestDto);

    List<RoleDto.RoleResponseDto> getAllRoles();

    RoleDto.RoleResponseDto getRoleById(Long roleId);

    RoleDto.RoleResponseDto getRoleByName(String roleName);

    RoleDto.RoleResponseDto updateRole(Long roleId, RoleDto.RoleUpdateRequestDto updateRequestDto);

    void deleteRole(Long roleId);

    List<PermissionDto> getAllPermissions();
} 