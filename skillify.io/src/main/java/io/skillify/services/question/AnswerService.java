package io.skillify.services.question;

import java.util.List;
import java.util.UUID;

import io.skillify.dtos.question.AnswerDto;
import io.skillify.dtos.question.AnswerDto.BatchSubmissionResponse;
import io.skillify.dtos.question.AnswerDto.BatchSubmitAnswerRequest;
import io.skillify.dtos.question.AnswerDto.SubmitAnswerRequest;
import io.skillify.models.question.Answer.AnswerId;

public interface AnswerService {

    AnswerDto.Response submitAnswer(SubmitAnswerRequest request);

    BatchSubmissionResponse submitBatchAnswers(BatchSubmitAnswerRequest request);

    AnswerDto.Response getAnswer(AnswerId id);

    List<AnswerDto.Response> getAnswersByTestAndCandidate(UUID testId, UUID candidateId);

    List<AnswerDto.Response> getAnswersByQuestionAndCandidate(UUID questionId, UUID candidateId);

    Float calculateAverageScoreByTestAndCandidate(UUID testId, UUID candidateId);

    Long countCorrectAnswersByTestAndCandidate(UUID testId, UUID candidateId);

    Long countTotalAnswersByTestAndCandidate(UUID testId, UUID candidateId);

    void deleteAnswer(AnswerId id);
}
