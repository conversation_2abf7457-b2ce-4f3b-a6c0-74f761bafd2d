package io.skillify.services.question;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.dtos.question.QuestionDto;
import io.skillify.dtos.question.QuestionDto.BaseQuestion;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.BatchDeleteQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchDeleteQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.CreateQuestionRequest;
import io.skillify.dtos.question.QuestionDto.QuestionMetadata;
import io.skillify.dtos.question.QuestionDto.QuestionUpdateItem;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.mappers.QuestionMapper;
import io.skillify.models.question.Question;
import io.skillify.models.test.Test;
import io.skillify.repositories.question.QuestionRepository;
import io.skillify.repositories.test.TestRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
@Transactional
public class QuestionServiceImpl implements QuestionService {
    Logger logger = LoggerFactory.getLogger(QuestionServiceImpl.class);

    private final QuestionRepository questionRepository;
    private final TestRepository testRepository;
    private final QuestionMapper questionMapper;

    @Override
    public QuestionDto.Response createQuestion(UUID testId, CreateQuestionRequest request) {
        logger.info("Creating question for test with ID: {}", testId);

        Test test = testRepository.findById(testId)
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + testId));

        Question question = questionMapper.toEntity(request);
        question.setTest(test);

        // If the order is specified, check if it is already taken
        if (question.getOrder() != null) {
            if (questionRepository.existsByTestIdAndOrder(testId, question.getOrder())) {
                logger.info("Question with order {} already exists, shifting questions", question.getOrder());
                // Shift all questions with order >= the specified order up by one
                questionRepository.incrementOrdersGreaterThanOrEqual(testId, question.getOrder());
            }
        }

        // If order is not specified, set it to the next available order
        if (question.getOrder() == null) {
            Integer maxOrder = questionRepository.findMaxOrderByTestId(testId);
            question.setOrder(maxOrder != null ? maxOrder + 1 : 1);
        }

        Question savedQuestion = questionRepository.save(question);
        return questionMapper.toDto(savedQuestion);
    }

    @Override
    @Transactional(readOnly = true)
    public QuestionDto.Response getQuestion(UUID id) {
        logger.info("Fetching question with ID: {}", id);

        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Question not found with ID: " + id));

        return questionMapper.toDto(question);
    }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionDto.Response> getQuestionsByTest(UUID testId) {
        logger.info("Fetching questions for test with ID: {}", testId);

        List<Question> questions = questionRepository.findByTestId(testId);
        return questionMapper.toDtoList(questions);
    }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionDto.Response> getQuestionsByTestOrdered(UUID testId) {
        logger.info("Fetching ordered questions for test with ID: {}", testId);

        List<Question> questions = questionRepository.findByTestIdOrderByOrder(testId);
        return questionMapper.toDtoList(questions);
    }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionDto.Response> getQuestionsByTestAndType(UUID testId, String type) {
        logger.info("Fetching questions for test with ID: {} and type: {}", testId, type);

        List<Question> questions = questionRepository.findByTestIdAndType(testId, type);
        return questionMapper.toDtoList(questions);
    }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionDto.Response> getQuestionsByTestAndDifficulty(UUID testId, String difficulty) {
        logger.info("Fetching questions for test with ID: {} and difficulty: {}", testId, difficulty);

        List<Question> questions = questionRepository.findByTestIdAndDifficulty(testId, difficulty);
        return questionMapper.toDtoList(questions);
    }

    @Override
    public QuestionDto.Response updateQuestion(UUID id, BaseQuestion request) {
        logger.info("Updating question with ID: {}", id);

        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Question not found with ID: " + id));

        questionMapper.updateEntity(question, request);

        Question updatedQuestion = questionRepository.saveAndFlush(question);
        logger.info("Successfully updated question with ID: {}", id);

        return questionMapper.toDto(updatedQuestion);
    }

    @Override
    public void deleteQuestion(UUID id) {
        logger.info("Deleting question with ID: {}", id);

        if (!questionRepository.existsById(id)) {
            throw new ResourceNotFoundException("Question not found with ID: " + id);
        }

        UUID testIdForReorder = questionRepository.getTestIdByQuestionId(id);
        Integer deletedQuestionOrder = questionRepository.getOrderByQuestionId(id);

        questionRepository.deleteById(id);
        logger.info("Deleted question with ID: {}", id);

        if (testIdForReorder != null && deletedQuestionOrder != null) {
             questionRepository.decrementOrdersGreaterThan(testIdForReorder, deletedQuestionOrder);
             logger.info("Reordered questions for test with ID: {}", testIdForReorder);
        } else {
            logger.warn("Could not reorder questions as testId or order was null for deleted questionId {}", id);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public long countQuestionsByTest(UUID testId) {
        logger.info("Counting questions for test with ID: {}", testId);

        return questionRepository.countByTestId(testId);
    }

    @Override
    @Transactional
    public BatchCreateQuestionsResponse batchCreateQuestions(UUID testId, BatchCreateQuestionsRequest request) {
        logger.info("Batch creating {} questions for test with ID: {}",
                request.getQuestions().size(), testId);

        Test test = testRepository.findById(testId)
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + testId));

        Integer maxOrder = questionRepository.findMaxOrderByTestId(testId);
        int nextOrder = maxOrder != null ? maxOrder + 1 : 1;

        List<Question> questions = new ArrayList<>();

        for (QuestionDto.BaseQuestion baseQuestion : request.getQuestions()) {
            Question question = questionMapper.baseToEntity(baseQuestion);
            question.setTest(test);

            if (question.getOrder() == null) {
                question.setOrder(nextOrder++);
            } else {
                if (questionRepository.existsByTestIdAndOrder(testId, question.getOrder())) {
                    questionRepository.incrementOrdersGreaterThanOrEqual(testId, question.getOrder());
                }
            }
            questions.add(question);
        }

        List<Question> savedQuestions = questionRepository.saveAll(questions);

        QuestionMetadata metadata = null;
        if (!savedQuestions.isEmpty()) {
            Question firstQuestion = savedQuestions.get(0);
            metadata = QuestionMetadata.builder()
                    .createdAt(firstQuestion.getCreatedAt())
                    .updatedAt(firstQuestion.getUpdatedAt())
                    .createdBy(firstQuestion.getCreatedBy() != null ? firstQuestion.getCreatedBy().getEmail() : null)
                    .updatedBy(firstQuestion.getUpdatedBy() != null ? firstQuestion.getUpdatedBy().getEmail() : null)
                    .build();
        }
        
        List<QuestionDto.QuestionContent> questionContents = questionMapper.toQuestionContentList(savedQuestions);

        return BatchCreateQuestionsResponse.builder()
                .testId(testId)
                .createdQuestions(savedQuestions.size())
                .metadata(metadata)
                .questions(questionContents)
                .build();
    }

    @Override
    @Transactional
    public BatchDeleteQuestionsResponse batchDeleteQuestions(UUID testId, BatchDeleteQuestionsRequest request) {
        logger.info("Batch deleting {} questions for test with ID: {}", request.getQuestionIds().size(), testId);

        List<Question> questionsToDelete = questionRepository.findByTestIdAndQuestionIds(
                testId, request.getQuestionIds());

        List<UUID> foundQuestionIds = questionsToDelete.stream()
                .map(Question::getId)
                .toList();

        List<UUID> notFoundIds = request.getQuestionIds().stream()
                .filter(id -> !foundQuestionIds.contains(id))
                .toList();

        if (questionsToDelete.isEmpty()) {
            logger.warn("No questions found for deletion in test with ID: {}", testId);
            return BatchDeleteQuestionsResponse.builder()
                    .testId(testId)
                    .deletedQuestions(0)
                    .failedIds(request.getQuestionIds())
                    .build();
        }

        Map<UUID, Integer> questionOrders = questionsToDelete.stream()
            .filter(q -> q.getOrder() != null)
            .collect(Collectors.toMap(Question::getId, Question::getOrder, (o1, o2) -> o1, HashMap::new));

        questionRepository.deleteAllById(foundQuestionIds);
        logger.info("Batch deleted {} questions for test ID: {}", foundQuestionIds.size(), testId);

        List<Integer> distinctOrdersDeleted = questionOrders.values().stream().distinct().sorted(Comparator.reverseOrder()).toList();
        for(Integer deletedOrder : distinctOrdersDeleted) {
            questionRepository.decrementOrdersGreaterThan(testId, deletedOrder);
        }
        logger.info("Attempted reordering questions for test ID {}", testId);

        return BatchDeleteQuestionsResponse.builder()
                .testId(testId)
                .deletedQuestions(foundQuestionIds.size())
                .failedIds(notFoundIds)
                .build();
    }

    @Override
    public BatchUpdateQuestionsResponse batchUpdateQuestions(UUID testId, BatchUpdateQuestionsRequest request) {
        logger.info("Batch updating {} questions for test with ID: {}", request.getQuestions().size(), testId);
        Test test = testRepository.findById(testId)
            .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + testId));

        List<QuestionDto.Response> resultingQuestions = new ArrayList<>();
        int createdCount = 0;
        int updatedCount = 0;
        int deletedCount = 0;

        List<QuestionUpdateItem> itemsToCreate = new ArrayList<>();
        Map<UUID, QuestionUpdateItem> itemsToUpdateMap = new HashMap<>();
        List<UUID> idsToDelete = new ArrayList<>();

        for (QuestionUpdateItem item : request.getQuestions()) {
            if (item.isDeleted()) {
                if (item.getId() != null) {
                    idsToDelete.add(item.getId());
                }
            } else if (item.getId() == null) {
                itemsToCreate.add(item);
            } else {
                itemsToUpdateMap.put(item.getId(), item);
            }
        }

        // Delete questions
        if (!idsToDelete.isEmpty()) {
            List<Question> questionsFoundForDeletion = questionRepository.findAllById(idsToDelete);
            List<UUID> actualIdsDeleted = questionsFoundForDeletion.stream().map(Question::getId).toList();
            
            if (!actualIdsDeleted.isEmpty()) {
                Map<UUID, Integer> ordersOfDeleted = questionsFoundForDeletion.stream()
                    .filter(q -> q.getTest().getId().equals(testId) && q.getOrder() != null)
                    .collect(Collectors.toMap(Question::getId, Question::getOrder));

                questionRepository.deleteAllByIdInBatch(actualIdsDeleted);
                deletedCount = actualIdsDeleted.size();

                List<Integer> distinctOrdersDeleted = ordersOfDeleted.values().stream().distinct().sorted(Comparator.reverseOrder()).toList();
                for(Integer deletedOrder : distinctOrdersDeleted) {
                    questionRepository.decrementOrdersGreaterThan(testId, deletedOrder);
                }
            }
        }

        // Create new questions
        if (!itemsToCreate.isEmpty()) {
            Integer maxOrder = questionRepository.findMaxOrderByTestId(testId);
            int nextOrder = (maxOrder != null ? maxOrder : 0) + 1;
            List<Question> newQuestions = new ArrayList<>();
            for (QuestionUpdateItem item : itemsToCreate) {
                Question newQuestion = questionMapper.baseToEntity(item);
                newQuestion.setTest(test);
                if (item.getOrder() == null) {
                    newQuestion.setOrder(nextOrder++);
                } else {
                    if (questionRepository.existsByTestIdAndOrder(testId, item.getOrder())) {
                        questionRepository.incrementOrdersGreaterThanOrEqual(testId, item.getOrder());
                    }
                    newQuestion.setOrder(item.getOrder());
                }
                newQuestions.add(newQuestion);
            }
            List<Question> savedNewQuestions = questionRepository.saveAll(newQuestions);
            savedNewQuestions.forEach(q -> resultingQuestions.add(questionMapper.toDto(q)));
            createdCount = savedNewQuestions.size();
        }

        // Update existing questions
        if (!itemsToUpdateMap.isEmpty()) {
            List<Question> questionsToUpdate = questionRepository.findAllById(itemsToUpdateMap.keySet());
            List<Question> updatedQuestions = new ArrayList<>();
            for (Question question : questionsToUpdate) {
                if (question.getTest().getId().equals(testId)) {
                    QuestionUpdateItem item = itemsToUpdateMap.get(question.getId());
                    if (item.getOrder() != null && !item.getOrder().equals(question.getOrder())) {
                        if (questionRepository.existsByTestIdAndOrderAndNotId(testId, item.getOrder(), question.getId())) {
                            questionRepository.incrementOrdersGreaterThanOrEqual(testId, item.getOrder());
                        }
                    }
                    questionMapper.updateEntity(question, item);
                    updatedQuestions.add(question);
                }
            }
            if (!updatedQuestions.isEmpty()) {
                List<Question> savedUpdatedQuestions = questionRepository.saveAll(updatedQuestions);
                savedUpdatedQuestions.forEach(q -> resultingQuestions.add(questionMapper.toDto(q)));
                updatedCount = savedUpdatedQuestions.size();
            }
        }
        
        return BatchUpdateQuestionsResponse.builder()
                .testId(testId)
                .createdCount(createdCount)
                .updatedCount(updatedCount)
                .deletedCount(deletedCount)
                .resultingQuestions(resultingQuestions)
                .build();
    }

    // TODO: Spaghetti code, refactor later
    @Override
    @Transactional(readOnly = true)
    public Object getTestQuestionsWithFilters(UUID testId, String metric, String difficulty, String sort, String type) {
        logger.info("Fetching questions for test with ID: {} with filters - metric: {}, difficulty: {}, sort: {}, type: {}",
                testId, metric, difficulty, sort, type);
        if ("count".equals(metric)) {
            if (difficulty != null && type != null) {
                return questionRepository.countByTestIdAndDifficultyAndType(testId, difficulty, type);
            } else if (difficulty != null) {
                return questionRepository.countByTestIdAndDifficulty(testId, difficulty);
            } else if (type != null) {
                return questionRepository.countByTestIdAndType(testId, type);
            } else {
                return questionRepository.countByTestId(testId);
            }
        }

        List<Question> questions;
        if ("ordered".equals(sort)) {
            if (difficulty != null && type != null) {
                questions = questionRepository.findByTestIdAndDifficultyAndTypeOrderByOrderAsc(testId, difficulty, type);
            } else if (difficulty != null) {
                questions = questionRepository.findByTestIdAndDifficultyOrderByOrderAsc(testId, difficulty);
            } else if (type != null) {
                questions = questionRepository.findByTestIdAndTypeOrderByOrderAsc(testId, type);
            } else {
                questions = questionRepository.findByTestIdOrderByOrder(testId);
            }
        } else {
            if (difficulty != null && type != null) {
                questions = questionRepository.findByTestIdAndDifficultyAndType(testId, difficulty, type);
            } else if (difficulty != null) {
                questions = questionRepository.findByTestIdAndDifficulty(testId, difficulty);
            } else if (type != null) {
                questions = questionRepository.findByTestIdAndType(testId, type);
            } else {
                questions = questionRepository.findByTestId(testId);
            }
        }
        return questionMapper.toDtoList(questions);
    }
}
