package io.skillify.services.question;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.JsonNode;

import io.skillify.dtos.question.AnswerDto;
import io.skillify.dtos.question.AnswerDto.BatchSubmissionResponse;
import io.skillify.dtos.question.AnswerDto.BatchSubmitAnswerRequest;
import io.skillify.dtos.question.AnswerDto.SubmitAnswerRequest;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.mappers.AnswerMapper;
import io.skillify.models.question.Answer;
import io.skillify.models.question.Answer.AnswerId;
import io.skillify.models.question.Question;
import io.skillify.models.test.Test;
import io.skillify.models.user.User;
import io.skillify.repositories.question.AnswerRepository;
import io.skillify.repositories.question.QuestionRepository;
import io.skillify.repositories.test.TestRepository;
import io.skillify.repositories.user.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class AnswerServiceImpl implements AnswerService {

    private final AnswerRepository answerRepository;
    private final QuestionRepository questionRepository;
    private final TestRepository testRepository;
    private final UserRepository userRepository;
    private final AnswerMapper answerMapper;

    @Override
    @Transactional
    public AnswerDto.Response submitAnswer(SubmitAnswerRequest request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User currentUser = userRepository.findByEmail(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));
        UUID candidateId = currentUser.getId();

        log.info("Submitting answer for question ID: {} by candidate ID: {}", request.getQuestionId(), candidateId);

        Test test = testRepository.findById(request.getTestId())
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + request.getTestId()));

        Question question = questionRepository.findById(request.getQuestionId())
                .orElseThrow(
                        () -> new ResourceNotFoundException("Question not found with ID: " + request.getQuestionId()));

        // If an answer already exists
        // assume we always create or overwrite
        Optional<Answer> existingAnswerOpt = answerRepository.findByTestIdAndQuestionIdAndCandidateId(
                test.getId(), question.getId(), candidateId);

        Answer answer;
        if (existingAnswerOpt.isPresent()) {
            log.info("Updating existing answer for question ID: {} by candidate ID: {}",
                    request.getQuestionId(), candidateId);
            answer = existingAnswerOpt.get();
            answer.setAnswer(request.getAnswer());
        } else {
            log.info("Creating new answer for question ID: {} by candidate ID: {}",
                    request.getQuestionId(), candidateId);
            answer = answerMapper.toEntity(request);
            answer.setTest(test);
            answer.setQuestion(question);
            answer.setCandidate(currentUser);
            answer.setTestId(test.getId());
            answer.setQuestionId(question.getId());
            answer.setCandidateId(candidateId);
        }
        answer.setAnswer(request.getAnswer());

        gradeAnswer(answer, question);

        Answer savedAnswer = answerRepository.save(answer);
        return answerMapper.toDto(savedAnswer);
    }

    @Override
    @Transactional
    public BatchSubmissionResponse submitBatchAnswers(BatchSubmitAnswerRequest request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();

        log.info("Submitting batch answers for test ID: {} by username: {}", request.getTestId(), username);

        Test test = testRepository.findById(request.getTestId())
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + request.getTestId()));

        User currentUser = userRepository.findByEmail(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));
        UUID candidateId = currentUser.getId();

        List<Answer> answersToSave = new ArrayList<>();
        List<AnswerDto.Response> responseList = new ArrayList<>();
        int submittedCount = 0;
        int correctAnswerCount = 0;
        float totalScoreSum = 0;

        Set<UUID> questionIdsInBatch = request.getAnswers().stream()
                .map(AnswerDto.AnswerRequest::getQuestionId)
                .collect(Collectors.toSet());
        Set<UUID> existingAnsweredQuestionIds = answerRepository.findQuestionIdsByTestIdAndCandidateIdAndQuestionIds(
                test.getId(), candidateId, questionIdsInBatch);

        for (AnswerDto.AnswerRequest answerRequest : request.getAnswers()) {
            UUID questionId = answerRequest.getQuestionId();

            if (existingAnsweredQuestionIds.contains(questionId)) {
                log.warn("Answer already exists for question ID: {} by candidate ID: {}. Skipping in batch.",
                        questionId, candidateId);
                continue; // Skip this answer
            }

            Question question = questionRepository.findById(questionId)
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Question not found with ID: " + questionId));

            Answer answer = Answer.builder()
                    .testId(test.getId())
                    .questionId(question.getId())
                    .candidateId(candidateId)
                    .test(test)
                    .question(question)
                    .candidate(currentUser)
                    .answer(answerRequest.getAnswer())
                    .build();

            gradeAnswer(answer, question);

            submittedCount++;
            if (Boolean.TRUE.equals(answer.getIsCorrect())) {
                correctAnswerCount++;
            }

            totalScoreSum += Optional.ofNullable(answer.getScore()).orElse(0.0f);

            answersToSave.add(answer);
        }

        List<Answer> savedAnswers = answerRepository.saveAll(answersToSave);

        // Map all saved answers to DTOs
        responseList.addAll(answerMapper.toDtoList(savedAnswers));

        float averageScore = submittedCount == 0 ? 0 : totalScoreSum / submittedCount;

        return BatchSubmissionResponse.builder()
                .testId(test.getId())
                .candidateId(candidateId)
                .answeredQuestions(submittedCount) // Number of answers actually processed/saved in this batch
                .correctAnswers(correctAnswerCount)
                .averageScore(averageScore) // Average score, considering partial credit (like multiple mcq)
                .answerDetails(responseList)
                .build();
    }

    private void gradeAnswer(Answer answer, Question question) {
        String questionType = question.getType();
        JsonNode submittedAnswerNode = answer.getAnswer();
        JsonNode correctAnswerNode = question.getCorrectAnswer();

        // Default
        answer.setIsCorrect(null);
        answer.setScore(null);

        if (questionType == null) {
            log.warn("Question type is null for Question ID: {}. Cannot grade.", question.getId());
            answer.setIsCorrect(false);
            answer.setScore(0.0f);
            return;
        }

        switch (questionType.toUpperCase()) {
            case "MCQ":
                submitMCQAnswer(answer, submittedAnswerNode, correctAnswerNode);
                break;

            case "OPEN_ENDED":
                log.debug("Open-ended question ID: {} requires manual grading.", question.getId());
                break;

            case "CODING":
                // TODO: to be implemented
                log.debug("Coding question ID: {} grading not implemented yet.", question.getId());
                break;

            default:
                log.warn("Unsupported question type for auto-grading: {}. Question ID: {}",
                        questionType, question.getId());
                answer.setIsCorrect(false);
                answer.setScore(0.0f);
                break;
        }
    }

    private void submitMCQAnswer(Answer answer, JsonNode submittedAnswerNode, JsonNode correctAnswerNode) {
        // Default to incorrect
        answer.setIsCorrect(false);
        answer.setScore(0.0f);

        if (submittedAnswerNode == null || submittedAnswerNode.isNull() ||
                correctAnswerNode == null || correctAnswerNode.isNull()) {
            log.warn("Submitted answer or correct answer JSON is null or missing for answer: T={}, Q={}, C={}",
                    answer.getTestId(), answer.getQuestionId(), answer.getCandidateId());
            return;
        }

        try {
            // Single-Choice
            if (submittedAnswerNode.has("selectedOption") && correctAnswerNode.has("value")) {
                String submitted = submittedAnswerNode.get("selectedOption").asText(null);
                String correct = correctAnswerNode.get("value").asText(null);

                if (submitted != null && correct != null) {
                    boolean isCorrect = submitted.equalsIgnoreCase(correct);
                    answer.setIsCorrect(isCorrect);
                    answer.setScore(isCorrect ? 1.0f : 0.0f);
                    log.debug("Graded Single-Choice MCQ: Q={}, Correct={}, Score={}",
                            answer.getQuestionId(), answer.getIsCorrect(), answer.getScore());
                } else {
                    log.warn("Single-choice MCQ fields present but contain null text values. Q={}, C={}",
                            answer.getQuestionId(), answer.getCandidateId());
                }
                return;
            }

            // Multiple-Choice
            else if (submittedAnswerNode.has("selectedOptions") && correctAnswerNode.has("values")) {
                JsonNode submittedOptionsNode = submittedAnswerNode.get("selectedOptions");
                JsonNode correctOptionsNode = correctAnswerNode.get("values");

                if (submittedOptionsNode != null && submittedOptionsNode.isArray() &&
                        correctOptionsNode != null && correctOptionsNode.isArray()) {

                    // Extract submitted options
                    Set<String> submittedSet = StreamSupport.stream(submittedOptionsNode.spliterator(), false)
                            .map(JsonNode::asText)
                            .filter(s -> s != null && !s.isBlank())
                            .collect(Collectors.toSet());

                    // Extract correct options
                    Set<String> correctSet = StreamSupport.stream(correctOptionsNode.spliterator(), false)
                            .map(JsonNode::asText)
                            .filter(s -> s != null && !s.isBlank())
                            .collect(Collectors.toSet());

                    int numTotalCorrect = correctSet.size();

                    // How many submitted options are actually correct
                    Set<String> intersection = new HashSet<>(submittedSet);
                    intersection.retainAll(correctSet);
                    int numCorrectlySelected = intersection.size();

                    // Calculate partial score: (correctly selected) / (total correct options)
                    float score = (float) numCorrectlySelected / numTotalCorrect;
                    answer.setScore(score);

                    // Check for perfect match: selected set must be identical to correct set
                    // This means all correct options were selected AND no incorrect options were
                    // selected
                    boolean isPerfectMatch = submittedSet.equals(correctSet);
                    answer.setIsCorrect(isPerfectMatch);

                    log.debug(
                            "Graded Multi-Choice MCQ: Q={}, CorrectlySelected={}, TotalCorrect={}, Score={}, PerfectMatch={}",
                            answer.getQuestionId(), numCorrectlySelected, numTotalCorrect, score, isPerfectMatch);

                } else {
                    log.warn("Multi-choice MCQ fields present but are not arrays or are null/empty. Q={}, C={}",
                            answer.getQuestionId(), answer.getCandidateId());
                }
                return;
            }

            else {
                log.warn(
                        "MCQ answer JSON does not match known single-choice ('selectedOption'/'value') or multi-choice ('selectedOptions'/'values') patterns. Q={}, C={}. Submitted: [{}], Correct: [{}]",
                        answer.getQuestionId(), answer.getCandidateId(),
                        submittedAnswerNode.toString(), correctAnswerNode.toString());
            }

        } catch (Exception e) {
            log.error("Error processing MCQ answer JSON for Q={}, C={}: {}",
                    answer.getQuestionId(), answer.getCandidateId(), e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public AnswerDto.Response getAnswer(AnswerId id) {
        log.info("Fetching answer with ID: {}", id);
        Answer answer = answerRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Answer not found with ID: " + id));
        return answerMapper.toDto(answer);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AnswerDto.Response> getAnswersByTestAndCandidate(UUID testId, UUID candidateId) {
        log.info("Fetching answers for test ID: {} and candidate ID: {}", testId, candidateId);
        List<Answer> answers = answerRepository.findByTestIdAndCandidateId(testId, candidateId);
        return answerMapper.toDtoList(answers);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AnswerDto.Response> getAnswersByQuestionAndCandidate(UUID questionId, UUID candidateId) {
        log.info("Fetching answers for question ID: {} and candidate ID: {}", questionId, candidateId);
        List<Answer> answers = answerRepository.findByQuestionIdAndCandidateId(questionId, candidateId);
        return answerMapper.toDtoList(answers);
    }

    @Override
    @Transactional(readOnly = true)
    public Float calculateAverageScoreByTestAndCandidate(UUID testId, UUID candidateId) {
        log.info("Calculating average score for test ID: {} and candidate ID: {}",
                testId, candidateId);
        // Ensure the repository method correctly averages the 'score' column
        return answerRepository.calculateAverageScoreByTestAndCandidate(testId, candidateId);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countCorrectAnswersByTestAndCandidate(UUID testId, UUID candidateId) {
        log.info("Counting perfectly correct answers (isCorrect=true) for test ID: {} and candidate ID: {}", testId,
                candidateId);
        // Assumes repository method counts where is_correct = true
        return answerRepository.countCorrectAnswersByTestAndCandidate(testId, candidateId);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countTotalAnswersByTestAndCandidate(UUID testId, UUID candidateId) {
        log.info("Counting total answers submitted for test ID: {} and candidate ID: {}", testId, candidateId);
        return answerRepository.countTotalAnswersByTestAndCandidate(testId, candidateId);
    }

    @Override
    @Transactional
    public void deleteAnswer(AnswerId id) {
        log.info("Deleting answer with ID: {}", id);
        if (!answerRepository.existsById(id)) {
            throw new ResourceNotFoundException("Answer not found with ID: " + id);
        }
        answerRepository.deleteById(id);
    }
}
