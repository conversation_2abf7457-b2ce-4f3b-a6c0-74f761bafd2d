package io.skillify.services.TestAssignment;

import java.util.List;
import java.util.UUID;

import io.skillify.dtos.test.TestAssignmentDto;
import io.skillify.dtos.test.TestAssignmentDto.CreateTestAssignmentRequest;
import io.skillify.dtos.test.TestAssignmentDto.UpdateTestAssignmentRequest;
import io.skillify.models.test.TestAssignmentStatus;

public interface TestAssignmentService {

    List<TestAssignmentDto.Response> createTestAssignments(CreateTestAssignmentRequest request);

    TestAssignmentDto.Response getTestAssignment(UUID id);

    List<TestAssignmentDto.Response> getTestAssignmentsByTest(UUID testId);

    List<TestAssignmentDto.Response> getTestAssignmentsByCandidateEmail(String candidateEmail);

    List<TestAssignmentDto.Response> getTestAssignmentsByStatus(TestAssignmentStatus status);

    List<TestAssignmentDto.Response> getTestAssignmentsByTestAndStatus(UUID testId, TestAssignmentStatus status);

    TestAssignmentDto.Response getTestAssignmentByTestAndCandidateEmail(UUID testId, String candidateEmail);

    TestAssignmentDto.Response updateTestAssignment(UUID id, UpdateTestAssignmentRequest request);

    void deleteTestAssignment(UUID id);

    long countTestAssignmentsByTest(UUID testId);

    long countTestAssignmentsByTestAndStatus(UUID testId, TestAssignmentStatus status);
}
