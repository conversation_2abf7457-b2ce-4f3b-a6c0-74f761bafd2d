package io.skillify.services.TestAssignment;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.dtos.test.TestAssignmentDto;
import io.skillify.dtos.test.TestAssignmentDto.AssignmentDetail;
import io.skillify.dtos.test.TestAssignmentDto.CreateTestAssignmentRequest;
import io.skillify.dtos.test.TestAssignmentDto.UpdateTestAssignmentRequest;
import io.skillify.exceptions.BadRequestException;
import io.skillify.exceptions.ResourceAlreadyExistsException;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.mappers.TestAssignmentMapper;
import io.skillify.models.test.Test;
import io.skillify.models.test.TestAssignment;
import io.skillify.models.test.TestAssignmentStatus;
import io.skillify.repositories.test.TestAssignmentRepository;
import io.skillify.repositories.test.TestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TestAssignmentServiceImpl implements TestAssignmentService {

    private final TestAssignmentRepository testAssignmentRepository;
    private final TestRepository testRepository;
    private final TestAssignmentMapper testAssignmentMapper;

    @Override
    public List<TestAssignmentDto.Response> createTestAssignments(CreateTestAssignmentRequest request) {
        if (request == null || request.getTestId() == null || request.getAssignments() == null || request.getAssignments().isEmpty()) {
            throw new BadRequestException("Test ID and a non-empty list of assignment details are required.");
        }

        UUID testId = request.getTestId();
        List<AssignmentDetail> assignmentDetails = request.getAssignments();

        Test test = testRepository.findById(testId)
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + testId));

        List<TestAssignment> assignmentsToSave = new ArrayList<>();

        for (AssignmentDetail detail : assignmentDetails) {
            String normalizedEmail = detail.getEmail().toLowerCase();

            testAssignmentRepository.findByTestIdAndCandidateEmail(test.getId(), normalizedEmail).ifPresent(existing -> {
                log.warn("Assignment already exists for test ID: {} and candidate email: {}. Skipping this item in batch.", test.getId(), normalizedEmail);
                throw new ResourceAlreadyExistsException("Test assignment already exists for email: " + normalizedEmail + " and test ID: " + test.getId());
            });

            TestAssignment assignment = TestAssignment.builder()
                    .test(test)
                    .candidateEmail(normalizedEmail)
                    .status(TestAssignmentStatus.PENDING)
                    .build();
            assignmentsToSave.add(assignment);
        }

        List<TestAssignment> savedAssignments = testAssignmentRepository.saveAll(assignmentsToSave);
        log.info("Successfully created a batch of {} test assignments for test ID: {}", savedAssignments.size(), testId);
        return savedAssignments.stream()
                .map(testAssignmentMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public TestAssignmentDto.Response getTestAssignment(UUID id) {
        log.info("Fetching test assignment with ID: {}", id);
        TestAssignment assignment = testAssignmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Test assignment not found with ID: " + id));
        return testAssignmentMapper.toDto(assignment);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestAssignmentDto.Response> getTestAssignmentsByTest(UUID testId) {
        log.info("Fetching test assignments for test ID: {}", testId);
        List<TestAssignment> assignments = testAssignmentRepository.findByTestId(testId);
        return testAssignmentMapper.toDtoList(assignments);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestAssignmentDto.Response> getTestAssignmentsByCandidateEmail(String candidateEmail) {
        log.info("Fetching test assignments for candidate email: {}", candidateEmail);
        String normalizedEmail = candidateEmail.toLowerCase();
        List<TestAssignment> assignments = testAssignmentRepository.findByCandidateEmail(normalizedEmail);
        return testAssignmentMapper.toDtoList(assignments);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestAssignmentDto.Response> getTestAssignmentsByStatus(TestAssignmentStatus status) {
        log.info("Fetching test assignments with status: {}", status);
        List<TestAssignment> assignments = testAssignmentRepository.findByStatus(status);
        return testAssignmentMapper.toDtoList(assignments);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestAssignmentDto.Response> getTestAssignmentsByTestAndStatus(UUID testId, TestAssignmentStatus status) {
        log.info("Fetching test assignments for test ID: {} with status: {}", testId, status);
        List<TestAssignment> assignments = testAssignmentRepository.findByTestIdAndStatus(testId, status);
        return testAssignmentMapper.toDtoList(assignments);
    }

    @Override
    @Transactional(readOnly = true)
    public TestAssignmentDto.Response getTestAssignmentByTestAndCandidateEmail(UUID testId, String candidateEmail) {
        log.info("Fetching test assignment for test ID: {} and candidate email: {}", testId, candidateEmail);
        String normalizedEmail = candidateEmail.toLowerCase();
        TestAssignment assignment = testAssignmentRepository.findByTestIdAndCandidateEmail(testId, normalizedEmail)
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Test assignment not found for test ID: " + testId + " and candidate email: " + normalizedEmail));
        return testAssignmentMapper.toDto(assignment);
    }

    @Override
    public TestAssignmentDto.Response updateTestAssignment(UUID id, UpdateTestAssignmentRequest request) {
        log.info("Updating test assignment with ID: {} to status: {}", id, request.getStatus());
        TestAssignment assignment = testAssignmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Test assignment not found with ID: " + id));

        testAssignmentMapper.updateEntity(assignment, request);

        // Update timestamps based on status
        if (request.getStatus() == TestAssignmentStatus.IN_PROGRESS && assignment.getStartTime() == null) {
            assignment.setStartTime(OffsetDateTime.now());
        } else if (request.getStatus() == TestAssignmentStatus.COMPLETED && assignment.getEndTime() == null) {
            assignment.setEndTime(OffsetDateTime.now());
        }

        TestAssignment updatedAssignment = testAssignmentRepository.save(assignment);
        return testAssignmentMapper.toDto(updatedAssignment);
    }

    @Override
    public void deleteTestAssignment(UUID id) {
        log.info("Deleting test assignment with ID: {}", id);
        if (!testAssignmentRepository.existsById(id)) {
            throw new ResourceNotFoundException("Test assignment not found with ID: " + id);
        }
        testAssignmentRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public long countTestAssignmentsByTest(UUID testId) {
        log.info("Counting test assignments for test ID: {}", testId);
        return testAssignmentRepository.countByTestId(testId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countTestAssignmentsByTestAndStatus(UUID testId, TestAssignmentStatus status) {
        log.info("Counting test assignments for test ID: {} with status: {}", testId, status);
        return testAssignmentRepository.countByTestIdAndStatus(testId, status);
    }
}
