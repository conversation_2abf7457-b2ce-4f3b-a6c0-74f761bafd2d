package io.skillify.services.org;

import java.util.Collections;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import io.skillify.models.org.OrgPermission;
import io.skillify.models.org.OrgRoleMapping;
import io.skillify.models.org.Organization;
import io.skillify.models.org.OrganizationRole;
import io.skillify.models.user.User;
import io.skillify.repositories.org.OrgRoleMappingRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class OrgRoleService {

    private final OrgRoleMappingRepository orgRoleMappingRepository;

    public Set<OrgPermission> getUserOrganizationPermissions(UUID organizationId, User user) {
        return orgRoleMappingRepository.findByOrganizationIdAndUserId(organizationId, user.getId())
                .map(mapping -> mapping.getRole().getPermissions())
                .orElse(Collections.emptySet());
    }

    public Set<OrganizationRole> getUserRoles(Organization org, User user) {
        return orgRoleMappingRepository.findByOrganizationAndUser(org, user).stream()
            .map(OrgRoleMapping::getRole)
            .collect(Collectors.toSet());
    }

    @Transactional
    public void mapUserToOrgRole(Organization org, User user, OrganizationRole role) {
        if (org == null || user == null || role == null) {
            throw new IllegalArgumentException("Organization ID, user, and role must not be null");
        }
        OrgRoleMapping mapping = new OrgRoleMapping(org, user, role);
        orgRoleMappingRepository.save(mapping);
    }

    @Transactional
    public void deleteAllRoleMappingsForOrganization(Organization org) {
        orgRoleMappingRepository.deleteByOrganization(org);
    }
}
