package io.skillify.models.question;

import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.UUID;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.fasterxml.jackson.databind.JsonNode;

import io.skillify.models.test.Test;
import io.skillify.models.user.User;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "answers", indexes = {
    @Index(name = "idx_answer_test_id", columnList = "test_id"),
    @Index(name = "idx_answer_question_id", columnList = "question_id"),
    @Index(name = "idx_answer_candidate_id", columnList = "candidate_id"),
    @Index(name = "idx_answer_is_correct", columnList = "is_correct")
})
@EntityListeners(AuditingEntityListener.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@IdClass(Answer.AnswerId.class)
public class Answer {

    @Id
    @Column(name = "test_id")
    private UUID testId;

    @Id
    @Column(name = "question_id")
    private UUID questionId;

    @Id
    @Column(name = "candidate_id")
    private UUID candidateId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "test_id", insertable = false, updatable = false)
    private Test test;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", insertable = false, updatable = false)
    private Question question;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "candidate_id", insertable = false, updatable = false)
    private User candidate;

    @Column(name = "answer", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private JsonNode answer;

    @Column(name = "is_correct")
    private Boolean isCorrect;

    @Column(name = "score")
    private Float score;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false, columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime createdAt;

    @Version
    @Column(name = "version")
    private Long version;

    public void setTest(Test test) {
        this.test = test;
        if (test != null) {
            this.testId = test.getId();
        }
    }

    public void setQuestion(Question question) {
        this.question = question;
        if (question != null) {
            this.questionId = question.getId();
        }
    }

    public void setCandidate(User candidate) {
        this.candidate = candidate;
        if (candidate != null) {
            this.candidateId = candidate.getId();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnswerId implements Serializable {
        private static final long serialVersionUID = 1L;

        private UUID testId;
        private UUID questionId;
        private UUID candidateId;
    }
}
