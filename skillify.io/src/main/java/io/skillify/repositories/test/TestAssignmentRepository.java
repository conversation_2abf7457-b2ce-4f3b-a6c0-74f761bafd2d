package io.skillify.repositories.test;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import io.skillify.models.test.TestAssignment;
import io.skillify.models.test.TestAssignmentStatus;

@Repository
public interface TestAssignmentRepository extends JpaRepository<TestAssignment, UUID> {

    List<TestAssignment> findByTestId(UUID testId);

    List<TestAssignment> findByStatus(TestAssignmentStatus status);

    List<TestAssignment> findByTestIdAndStatus(UUID testId, TestAssignmentStatus status);

    long countByTestId(UUID testId);

    long countByTestIdAndStatus(UUID testId, TestAssignmentStatus status);

    Optional<TestAssignment> findByTestIdAndCandidateEmail(UUID testId, String candidateEmail);

    List<TestAssignment> findByCandidateEmail(String candidateEmail);

    List<TestAssignment> findByCandidateEmailAndStatus(String candidateEmail, TestAssignmentStatus status);

    @Modifying
    @Query("UPDATE TestAssignment ta SET ta.status = :status WHERE ta.id = :id")
    void updateStatusById(@Param("id") UUID id, @Param("status") TestAssignmentStatus status);
}
