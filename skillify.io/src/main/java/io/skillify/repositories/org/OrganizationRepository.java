package io.skillify.repositories.org;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import io.skillify.models.org.Organization;
import io.skillify.models.user.User;

@Repository
public interface OrganizationRepository extends JpaRepository<Organization, UUID> {
    Optional<Organization> findByName(String name);

    Optional<Organization> findByCreatedBy(User user);

    @NonNull
    Optional<Organization> findById(@NonNull UUID id);

    @Query("SELECT o FROM Organization o JOIN o.members m WHERE m.user.id = :userId")
    List<Organization> findAllByMemberId(@Param("userId") UUID userId);

    @Query("SELECT o FROM Organization o LEFT JOIN FETCH o.members m LEFT JOIN FETCH m.user WHERE o.id = :orgId")
    Optional<Organization> findByIdWithMembersAndUsers(@Param("orgId") UUID orgId);
}
