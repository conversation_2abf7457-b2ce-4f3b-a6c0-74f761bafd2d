package io.skillify.repositories.org;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;

import io.skillify.models.org.OrgRoleMapping;
import io.skillify.models.org.Organization;
import io.skillify.models.user.User;

public interface OrgRoleMappingRepository extends JpaRepository<OrgRoleMapping, OrgRoleMapping.OrgRoleMappingId> {
    Optional<OrgRoleMapping> findByOrganizationIdAndUserId(UUID organizationId, UUID userId);

    List<OrgRoleMapping> findByOrganizationAndUser(Organization organization, User user);

    void deleteByOrganization(Organization org);
}
