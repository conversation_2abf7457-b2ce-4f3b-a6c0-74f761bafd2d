package io.skillify.dtos.role;

import java.io.Serializable;
import java.util.Set;

import io.skillify.models.user.Role;
import lombok.Value;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

@Value
public class RoleDto implements Serializable {
    Long id;
    Role.RoleType name;
    Set<PermissionDto> permissions;

    @Schema(name = "RoleResponse", description = "DTO for Role details with permissions")
    public record RoleResponseDto(
        @Schema(description = "Unique ID of the role", example = "1")
        Long id,

        @Schema(description = "Unique name of the role", example = "ROLE_EDITOR", requiredMode = Schema.RequiredMode.REQUIRED)
        String name,

        @Schema(description = "Set of permissions associated with this role")
        Set<PermissionDto> permissions
    ) {}

    @Schema(name = "RoleCreateRequest", description = "DTO for creating a new role")
    public record RoleCreateRequestDto(
        @Schema(description = "Name for the new role", example = "ROLE_EDITOR", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "Role name cannot be blank")
        @Size(min = 3, max = 50, message = "Role name must be between 3 and 50 characters")
        String name,

        @Schema(description = "Set of permission names to assign to the new role. Permissions must exist.", example = "[\"USER_READ\", \"ARTICLE_EDIT\"]", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "At least one permission must be assigned to the role")
        Set<String> permissionNames
    ) {}

    @Schema(name = "RoleUpdateRequest", description = "DTO for updating an existing role")
    public record RoleUpdateRequestDto(
        @Schema(description = "New name for the role. If null or blank, name will not be updated.", example = "ROLE_SENIOR_EDITOR")
        @Size(min = 3, max = 50, message = "Role name must be between 3 and 50 characters")
        String name,

        @Schema(description = "New set of permission names for the role. Replaces existing permissions. If null, permissions will not be updated.", example = "[\"USER_READ\", \"ARTICLE_PUBLISH\"]")
        Set<String> permissionNames
    ) {}
}
