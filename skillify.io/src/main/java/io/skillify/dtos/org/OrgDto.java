package io.skillify.dtos.org;

import java.util.Set;
import java.util.UUID;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

public class OrgDto {
    @Data
    @Schema(name = "OrgCreateRequest", description = "Organization creation request")
    public static class CreateRequest {
        @NotBlank(message = "Organization name is required")
        @Size(min = 3, max = 255, message = "Organization name must be between 3 and 255 characters")
        @Schema(description = "Organization name", example = "Skillify Inc.")
        private String name;
    }

    @Data
    @Schema(name = "OrgUpdateRequest", description = "Organization update request")
    public static class UpdateRequest {
        @NotBlank(message = "Organization name is required")
        @Size(min = 3, max = 255, message = "Organization name must be between 3 and 255 characters")
        @Schema(description = "Organization name", example = "Skillify Corp.")
        private String name;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(name = "OrgResponse", description = "Organization response body")
    public static class Response {
        @Schema(description = "Organization ID", example = "a1b2c3d4-e5f6-7890-1234-567890abcdef")
        private UUID id;
        @Schema(description = "Organization name", example = "Skillify Inc.")
        private String name;
        @Schema(description = "ID of the user who created the organization", example = "f1e2d3c4-b5a6-7890-1234-567890abcdef")
        private String createdBy;
        @Schema(description = "Number of members in the organization", example = "5")
        private int memberCount;
    }

    @Data
    @Schema(name = "OrgDetailedResponse", description = "Detailed response body for organization operations")
    public static class DetailedResponse {
        @Schema(description = "Organization ID", example = "a1b2c3d4-e5f6-7890-1234-567890abcdef")
        private UUID id;
        @Schema(description = "Organization name", example = "Skillify Inc.")
        private String name;
        @Schema(description = "ID of the user who created the organization", example = "f1e2d3c4-b5a6-7890-1234-567890abcdef")
        private String createdBy;
        @Schema(description = "Set of members in the organization")
        private Set<MemberDetails> members;

        @Data
        @Schema(description = "Details of a member in the organization")
        public static class MemberDetails {
            @Schema(description = "User ID", example = "b1c2d3e4-f5a6-7890-1234-567890abcdef")
            private UUID userId;
            @Schema(description = "User email", example = "<EMAIL>")
            private String email;
            @Schema(description = "Set of roles for the user in the organization")
            private Set<String> roles;
        }
    }

    @Data
    @Schema(name = "OrgMemberResponse", description = "Response body for organization member operations")
    public static class MemberResponse {
        @Schema(description = "User ID", example = "c1d2e3f4-a5b6-7890-1234-567890abcdef")
        private UUID userId;
        @Schema(description = "Username", example = "johndoe")
        private String username;
        @Schema(description = "User email", example = "<EMAIL>")
        private String email;
        @Schema(description = "Set of roles for the user in the organization")
        private Set<String> roles;
    }

    @Data
    @Schema(name = "OrgAddMemberRequest", description = "Request body for adding a member to an organization")
    public static class AddMemberRequest {
        @NotBlank(message = "User email is required")
        @Schema(description = "User email", example = "<EMAIL>")
        private String userEmail;
        @NotBlank(message = "Role name is required")
        @Schema(description = "Role name", example = "ROLE_ORG_INTERVIEWER")
        private String roleName;
    }
}
