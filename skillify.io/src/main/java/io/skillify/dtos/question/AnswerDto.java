package io.skillify.dtos.question;

import java.util.List;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

public class AnswerDto {

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaseAnswer {
        @Schema(description = "The answer content")
        private JsonNode answer;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "AnswerResponse", description = "Detailed information about a submitted answer")
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BaseAnswer {
        @Schema(description = "Unique identifier of the test", example = "123e4567-e89b-12d3-a456-************")
        private UUID testId;

        @Schema(description = "Unique identifier of the question", example = "123e4567-e89b-12d3-a456-************")
        private UUID questionId;

        @Schema(description = "Unique identifier of the candidate", example = "123e4567-e89b-12d3-a456-************")
        private UUID candidateId;

        @Schema(description = "Whether the answer is correct")
        private Boolean isCorrect;

        @Schema(description = "Score awarded for the answer (0.0 to 100.0)", example = "85.5")
        private Float score;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "AnswerRequest", description = "Individual answer for a question")
    @EqualsAndHashCode(callSuper = true)
    public static class AnswerRequest extends BaseAnswer {
        @NotNull(message = "Question ID is required")
        @Schema(description = "Unique identifier of the question", required = true, example = "123e4567-e89b-12d3-a456-************")
        private UUID questionId;

        @NotNull(message = "Answer is required")
        @Schema(description = "The answer content", required = true)
        private JsonNode answer;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "SubmitAnswerRequest", description = "Request to submit a single answer for a question")
    @EqualsAndHashCode(callSuper = true)
    public static class SubmitAnswerRequest extends AnswerRequest {
        @NotNull(message = "Test ID is required")
        @Schema(description = "Unique identifier of the test", required = true, example = "123e4567-e89b-12d3-a456-************")
        private UUID testId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "BatchSubmitAnswerRequest", description = "Submit multiple answers for a test in one request")
    public static class BatchSubmitAnswerRequest {
        @NotNull(message = "Test ID is required")
        @Schema(description = "Unique identifier of the test", required = true, example = "123e4567-e89b-12d3-a456-************")
        private UUID testId;

        @NotEmpty(message = "At least one answer is required")
        @Size(min = 1, message = "At least one answer is required")
        @Valid
        @Schema(description = "List of answers to submit", required = true)
        private List<AnswerRequest> answers;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaseBatchSubmissionResponse {
        @Schema(description = "Unique identifier of the test", example = "123e4567-e89b-12d3-a456-************")
        private UUID testId;

        @Schema(description = "Unique identifier of the candidate", example = "123e4567-e89b-12d3-a456-************")
        private UUID candidateId;

        @Schema(description = "Number of questions answered", example = "8")
        private Integer answeredQuestions;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "BatchSubmissionResponse", description = "Detailed results of batch answer submission")
    @EqualsAndHashCode(callSuper = true)
    public static class BatchSubmissionResponse extends BaseBatchSubmissionResponse {
        @Schema(description = "Total questions in the test", example = "10")
        private Integer totalQuestions;

        @Schema(description = "Average score across all answers (0.0 to 100.0)", example = "75.5")
        private Float averageScore;

        @Schema(description = "Number of correctly answered questions", example = "6")
        private Integer correctAnswers;

        @Schema(description = "Detailed results for each submitted answer")
        private List<Response> answerDetails;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "TestAnswersResponse", description = "Collection of all candidate answers for a test")
    @EqualsAndHashCode(callSuper = false)
    public static class TestAnswersResponse {
        @Schema(description = "Unique identifier of the test", example = "123e4567-e89b-12d3-a456-************")
        private UUID testId;

        @Schema(description = "List of candidate responses for the test")
        private List<CandidateAnswers> candidateAnswers;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @Schema(description = "Collection of answers from a single candidate")
        public static class CandidateAnswers {
            @Schema(description = "Unique identifier of the candidate", example = "123e4567-e89b-12d3-a456-************")
            private UUID candidateId;

            @Schema(description = "All answers submitted by the candidate")
            private List<AnswerCompact> answers;

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            @Builder
            @JsonInclude(JsonInclude.Include.NON_NULL)
            @Schema(description = "Compact representation of an answer")
            public static class AnswerCompact {
                @Schema(description = "Unique identifier of the question", example = "123e4567-e89b-12d3-a456-************")
                private UUID questionId;

                @Schema(description = "The answer content")
                private JsonNode answer;

                @Schema(description = "Whether the answer is correct")
                private Boolean isCorrect;

                @Schema(description = "Score awarded for the answer (0.0 to 100.0)", example = "85.5")
                private Float score;

                @Schema(description = "Order of the question in the test", example = "1")
                private Integer questionOrder;
            }
        }
    }
}
