package io.skillify.dtos.user;

import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.Set;
import java.util.UUID;

import io.skillify.dtos.role.RoleDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;

@Value
@Getter
@Builder
public class UserDto implements Serializable {
    @Schema(name = "UserResponse", description = "Data Transfer Object for user details")
    public record UserResponseDto(
        @Schema(description = "Unique ID of the user")
        UUID id,

        @Schema(description = "Username", example = "john.doe")
        String username,

        @Schema(description = "Email address", example = "<EMAIL>")
        String email,

        @Schema(description = "Timestamp of the last login")
        OffsetDateTime lastLogin,

        @Schema(description = "Timestamp of user creation")
        OffsetDateTime createdAt,

        @Schema(description = "Timestamp of last update")
        OffsetDateTime updatedAt,

        @Schema(description = "Indicates if the user email is verified")
        boolean verified,

        @Schema(description = "Set of roles assigned to the user")
        Set<RoleDto.RoleResponseDto> roles
    ) {}

    @Schema(name = "UserCreateRequest", description = "Data Transfer Object for creating a new user by an admin")
    public record UserCreateRequestDto(
        @Schema(description = "Username for the new user", example = "new.user", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "Username cannot be blank")
        @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
        String username,

        @Schema(description = "Email for the new user", example = "<EMAIL>", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "Email cannot be blank")
        @Email(message = "Email should be valid")
        @Size(max = 100, message = "Email must be less than 100 characters")
        String email,

        @Schema(description = "Password for the new user", example = "Str0ngP@ssw0rd!", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "Password cannot be blank")
        @Size(min = 8, max = 100, message = "Password must be between 8 and 100 characters")
        String password,

        @Schema(description = "Set of role names to assign to the new user. Roles must exist.", example = "[\"ROLE_USER\"]")
        Set<String> roleNames,

        @Schema(description = "Flag to indicate if the user should be created as verified", example = "true", defaultValue = "false")
        Boolean verified
    ) {}

    @Schema(name = "UserProfileUpdateRequest", description = "Data Transfer Object for updating user's own profile information")
    public record UserProfileUpdateRequestDto(
        @Schema(description = "New username. If null or blank, will not be updated.", example = "john.doe.updated")
        @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
        String username,

        @Schema(description = "New email. If null or blank, will not be updated.", example = "<EMAIL>")
        @Email(message = "Email should be valid")
        @Size(max = 100, message = "Email must be less than 100 characters")
        String email
    ) {}

    @Schema(name = "AdminUserUpdateRequest", description = "Data Transfer Object for admin updating user's information")
    public record AdminUserUpdateRequestDto(
        @Schema(description = "New username. If null or blank, will not be updated.", example = "john.doe.updated")
        @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
        String username,

        @Schema(description = "New email. If null or blank, will not be updated.", example = "<EMAIL>")
        @Email(message = "Email should be valid")
        @Size(max = 100, message = "Email must be less than 100 characters")
        String email,

        @Schema(description = "Set of role names to assign to the user. Replaces existing roles. If null, roles are not changed.", example = "[\"ROLE_USER\", \"ROLE_EDITOR\"]")
        Set<String> roleNames,

        @Schema(description = "Set user's verified status. If null, status is not changed.", example = "true")
        Boolean verified
    ) {}

    @Schema(name = "PasswordChangeRequest", description = "Data Transfer Object for user changing their own password")
    public record PasswordChangeRequestDto(
        @Schema(description = "User's current password", example = "OldP@ssw0rd!", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "Old password cannot be blank")
        String oldPassword,

        @Schema(description = "New password for the user", example = "NewStr0ngP@ssw0rd!", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "New password cannot be blank")
        @Size(min = 8, max = 100, message = "New password must be between 8 and 100 characters")
        String newPassword
    ) {}

    @Schema(name = "AdminPasswordResetRequest", description = "Data Transfer Object for admin resetting a user's password")
    public record AdminPasswordResetRequestDto(
        @Schema(description = "New password to set for the user", example = "AdminResetP@ssw0rd!", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "New password cannot be blank")
        @Size(min = 8, max = 100, message = "New password must be between 8 and 100 characters")
        String newPassword
    ) {}
}
