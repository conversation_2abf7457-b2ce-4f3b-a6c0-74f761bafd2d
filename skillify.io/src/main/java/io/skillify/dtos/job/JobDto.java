package io.skillify.dtos.job;

import java.time.OffsetDateTime;
import java.util.UUID;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class JobDto {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "Request object for creating a new job")
    public static class CreateRequest {
        @Schema(description = "Job title", required = true)
        @NotBlank(message = "Title is required")
        @Size(min = 3, max = 255)
        private String title;

        @Schema(description = "Job description")
        @Size(max = 5000)
        private String description;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "Request object for updating an existing job")
    public static class UpdateRequest {
        @Schema(description = "Job title")
        @Size(min = 3, max = 255)
        private String title;

        @Schema(description = "Job description")
        @Size(max = 5000)
        private String description;

        @Schema(description = "Job status", example = "true")
        private Boolean isActive;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(description = "Response object containing job details")
    public static class Response {
        @Schema(description = "Job ID", example = "123e4567-e89b-12d3-a456-************")
        private UUID id;

        @Schema(description = "Job title")
        private String title;

        @Schema(description = "Job description")
        private String description;

        @Schema(description = "Organization ID", example = "123e4567-e89b-12d3-a456-************")
        private UUID organizationId;

        @Schema(description = "Job status", example = "true")
        private boolean isActive;

        @Schema(description = "User ID of the job creator", example = "123e4567-e89b-12d3-a456-************")
        private String createdBy;

        @Schema(description = "User ID of the user who last updated the job", example = "123e4567-e89b-12d3-a456-************")
        private String updatedBy;

        @Schema(description = "Date and time the job was last updated", example = "2025-07-01T12:00:00Z")
        private OffsetDateTime updatedAt;

        @Schema(description = "Date and time the job was created", example = "2021-07-01T12:00:00Z")
        private OffsetDateTime createdAt;
    }
}
