package io.skillify.mappers;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import io.skillify.dtos.question.QuestionDto;
import io.skillify.dtos.question.QuestionDto.BaseQuestion;
import io.skillify.dtos.question.QuestionDto.CreateQuestionRequest;
import io.skillify.models.question.Question;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface QuestionMapper {

    @Mapping(target = "testId", source = "test.id")
    @Mapping(target = "createdBy", source = "createdBy.email")
    @Mapping(target = "updatedBy", source = "updatedBy.email")
    QuestionDto.Response toDto(Question question);

    List<QuestionDto.Response> toDtoList(List<Question> questions);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "test", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    Question toEntity(CreateQuestionRequest request);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "test", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "type", source = "type", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "text", source = "text", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "options", source = "options", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "correctAnswer", source = "correctAnswer", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "difficulty", source = "difficulty", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "order", source = "order", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateEntity(@MappingTarget Question question, BaseQuestion request);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "test", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    Question baseToEntity(BaseQuestion baseQuestion);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "type", source = "type")
    @Mapping(target = "text", source = "text")
    @Mapping(target = "options", source = "options")
    @Mapping(target = "correctAnswer", source = "correctAnswer")
    @Mapping(target = "difficulty", source = "difficulty")
    @Mapping(target = "order", source = "order")
    QuestionDto.QuestionContent toQuestionContent(Question question);

    List<QuestionDto.QuestionContent> toQuestionContentList(List<Question> questions);
}
