package io.skillify.mappers;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import io.skillify.dtos.test.TestAssignmentDto;
import io.skillify.models.test.TestAssignment;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TestAssignmentMapper {

    @Mapping(target = "testId", source = "test.id")
    @Mapping(target = "candidateEmail", source = "candidateEmail")
    @Mapping(target = "createdBy", source = "createdBy.email")
    @Mapping(target = "updatedBy", source = "updatedBy.email")
    TestAssignmentDto.Response toDto(TestAssignment assignment);

    List<TestAssignmentDto.Response> toDtoList(List<TestAssignment> assignments);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "test", ignore = true)
    @Mapping(target = "candidateEmail", ignore = true)
    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    @Mapping(target = "score", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateEntity(@MappingTarget TestAssignment assignment, TestAssignmentDto.UpdateTestAssignmentRequest request);
}
