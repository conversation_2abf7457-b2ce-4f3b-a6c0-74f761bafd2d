{
  "extends": [
    "next/core-web-vitals",
    "plugin:@typescript-eslint/recommended",
    "plugin:react-hooks/recommended",
    "prettier"
  ],
  "plugins": ["@typescript-eslint", "react-hooks", "prettier"],
  "rules": {
    // "prettier/prettier": "warn",
    "react-hooks/rules-of-hooks": "error",
    "@typescript-eslint/no-unused-vars": "warn",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/no-explicit-any": "warn",
    "react/no-unescaped-entities": "off",
    "@next/next/no-html-link-for-pages": "warn"
  }
}
