"use client";
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MonacoCodeEditor } from "@/components/assessment/monaco-code-editor";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

import { Play, ArrowLeft, CheckCircle, XCircle, Clock, Save, Terminal } from "lucide-react";
import axios from "axios";
import { useSession } from "next-auth/react";
import ReactMarkdown from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import { PY_URL } from "@/config";
import toast from "react-hot-toast";

// Types
interface TestCase {
  input: string;
  expected_output: string;
  actualOutput?: string;
  passed?: boolean;
  weight: number;
}

interface EvaluationCriteria {
  constraints: string[];
  spaceComplexity: string;
  timeComplexity: string;
}

interface GradingRules {
  codeQualityWeight: number;
  efficiencyWeight: number;
  partialCredit: boolean;
  testCaseWeight: number;
}

interface Metadata {
  difficulty: "EASY" | "MEDIUM" | "HARD";
  estimatedDuration: number;
  tags: string[];
}

interface CodingQuestion {
  evaluationCriteria: EvaluationCriteria;
  gradingRules: GradingRules;
  language: string;
  metadata: Metadata;
  order: number;
  starterCode: string;
  testCases: TestCase[];
  text: string;
  title: string;
}

// API function to fetch coding question
const fetchCodingQuestion = async (testId: string, questionId: string): Promise<CodingQuestion> => {
  try {
    const response = await axios.get(`${PY_URL}/assessments/${testId}/code/${questionId}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching coding question:", error);
    throw error;
  }
};

const CodingDashboard = () => {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const assessmentId = params.assessment_id as string;
  const questionId = params.question_id as string;

  const [question, setQuestion] = useState<CodingQuestion | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [code, setCode] = useState("");
  const [testResults, setTestResults] = useState<TestCase[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(900); // 15 minutes default
  const [output, setOutput] = useState("");

  // Fetch coding question on component mount
  useEffect(() => {
    const loadQuestion = async () => {
      try {
        setLoading(true);
        const questionData = await fetchCodingQuestion(assessmentId, questionId);
        setQuestion(questionData);
        setCode(questionData.starterCode || "");
        setTimeRemaining((questionData.metadata.estimatedDuration || 15) * 60); // Convert minutes to seconds
      } catch (err: any) {
        console.error("Error loading question:", err);

        // Handle specific API errors
        if (err.response?.status === 404) {
          setError("Coding question not found. Please check the URL and try again.");
        } else if (err.response?.status === 403) {
          setError("You don't have permission to access this coding question.");
        } else if (err.response?.status === 410) {
          setError("This assessment has expired and is no longer available.");
        } else if (err.response?.data?.message) {
          setError(err.response.data.message);
        } else if (err.code === "NETWORK_ERROR" || !err.response) {
          setError("Network error. Please check your internet connection and try again.");
        } else {
          setError(err.message || "Failed to load coding question. Please try again.");
        }
      } finally {
        setLoading(false);
      }
    };

    loadQuestion();
  }, [assessmentId, questionId]);

  // Timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev <= 1) {
          handleSaveAndReturn();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const handleRunCode = async () => {
    if (!question) return;

    setIsRunning(true);
    setOutput("");

    try {
      // API call to test code using your exact endpoint
      const response = await axios.post(`${PY_URL}/assessments/${assessmentId}/test-code`, {
        question_id: questionId,
        code,
        language: question.language,
      });

      console.log("Code execution result:", response.data);

      // Process the API response based on your API structure
      const results = response.data.results;

      // Map the results to our test case format
      const processedResults = question.testCases.map((testCase, index) => {
        const apiResult = results?.testResults?.[index] || results?.[index];
        return {
          ...testCase,
          actualOutput: apiResult?.output || apiResult?.actualOutput || "No output",
          passed: apiResult?.passed || false,
        };
      });

      setTestResults(processedResults);

      // Set output with execution details
      const executionOutput =
        results?.execution_output || results?.output || results?.message || "";
      const summary =
        `Code executed successfully!\n\n${executionOutput ? `Output:\n${executionOutput}\n\n` : ""}Test Results:\n` +
        processedResults
          .map((result, i) => `Test ${i + 1}: ${result.passed ? "✅ PASSED" : "❌ FAILED"}`)
          .join("\n");

      setOutput(summary);
    } catch (error: any) {
      console.error("Code execution error:", error);

      let errorMessage = "Error executing code: ";
      if (error.response?.status === 410) {
        errorMessage = "Assessment has expired. Code execution is no longer available.";
      } else if (error.response?.status === 403) {
        errorMessage = "You don't have permission to execute code for this assessment.";
      } else if (error.response?.status === 404) {
        errorMessage = "Assessment or question not found.";
      } else if (error.response?.data?.message) {
        errorMessage += error.response.data.message;
      } else if (error.code === "NETWORK_ERROR" || !error.response) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else {
        errorMessage += error.message || "Unknown error occurred";
      }

      setOutput(errorMessage);

      // Fallback to mock results for demo
      const mockResults = question.testCases.map((testCase) => {
        const passed = Math.random() > 0.3;
        return {
          ...testCase,
          actualOutput: passed ? testCase.expected_output : "wrong_output",
          passed,
        };
      });

      setTestResults(mockResults);
    } finally {
      setIsRunning(false);
    }
  };

  const handleSaveAndReturn = async () => {
    if (!question) return;

    setIsSaving(true);

    try {
      // API call to submit coding answer using your exact endpoint
      const submissionData = {
        candidate_id: session?.user?.email,
        question_id: questionId,
        code,
        language: question.language,
        execution_time: Math.random() * 0.5, // Mock execution time
        memory_usage: Math.floor(Math.random() * 5000) + 1000, // Mock memory usage
      };

      toast.success("codeing question submited - [test mode]")

      // Update the assessment state with the coding answer
      const savedState = localStorage.getItem(`assessment_${assessmentId}_state`);
      if (savedState) {
        try {
          const state = JSON.parse(savedState);
          state.codingAnswers = {
            ...state.codingAnswers,
            [questionId]: submissionData,
          };
          localStorage.setItem(`assessment_${assessmentId}_state`, JSON.stringify(state));
        } catch (error) {
          console.error("Failed to update assessment state:", error);
        }
      }

      // Return to assessment
      router.push(`/assessments/${assessmentId}`);
    } catch (error: any) {
      console.error("Failed to save coding answer:", error);

      // Handle specific submission errors
      if (error.response?.status === 410) {
        alert("Assessment has expired. Your code cannot be submitted.");
      } else if (error.response?.status === 409) {
        alert("This coding question has already been submitted.");
      } else if (error.response?.status === 403) {
        alert("You don't have permission to submit this coding question.");
      } else if (error.response?.data?.message) {
        alert(`Submission failed: ${error.response.data.message}`);
      } else if (error.code === "NETWORK_ERROR" || !error.response) {
        alert("Network error. Please check your connection and try again.");
        // Don't navigate away for network errors
        return;
      } else {
        alert("Failed to submit coding answer. Please try again.");
      }

      // For demo purposes, still return to assessment even if API fails (except for network errors)
      router.push(`/assessments/${assessmentId}`);
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900">Loading question...</h2>
        </div>
      </div>
    );
  }

  if (error || !question) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">{error || "Question not found"}</h2>
          <Button onClick={() => router.push(`/assessments/${assessmentId}`)} className="mt-4">
            Return to Assessment
          </Button>
        </div>
      </div>
    );
  }

  const passedTests = testResults.filter((result) => result.passed).length;
  const totalTests = testResults.length;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/assessments/${assessmentId}`)}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Assessment
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-xl font-semibold text-gray-900">{question.title}</h1>
              <Badge
                variant={
                  question.metadata.difficulty === "EASY"
                    ? "default"
                    : question.metadata.difficulty === "MEDIUM"
                      ? "secondary"
                      : "destructive"
                }
              >
                {question.metadata.difficulty}
              </Badge>
            </div>
            <div className="flex items-center space-x-4">
              {/* <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span
                  className={`font-mono text-lg ${timeRemaining < 300 ? "text-red-600" : "text-gray-900"}`}
                >
                  {formatTime(timeRemaining)}
                </span>
              </div> */}
              <Button onClick={handleSaveAndReturn} disabled={isSaving}>
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? "Saving..." : "Save & Return"}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* LeetCode-style Layout */}
      <div className="flex h-[calc(100vh-4rem)]">
        {/* Left Panel - Problem Description (Full Height) */}
        <div className="w-1/2 border-r border-border bg-white">
          <div className="h-full flex flex-col">
            {/* Problem Header */}
            <div className="p-4 border-b border-border">
              <div className="flex items-center justify-between">
                <h1 className="text-xl font-semibold text-gray-900">{question.title}</h1>
                <Badge
                  className={`text-xs font-medium ${
                    question.metadata.difficulty === "EASY"
                      ? "bg-green-100 text-green-800 border-green-200"
                      : question.metadata.difficulty === "MEDIUM"
                        ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                        : "bg-red-100 text-red-800 border-red-200"
                  }`}
                >
                  {question.metadata.difficulty}
                </Badge>
              </div>

              {/* Tags */}
              {question.metadata.tags && question.metadata.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-3">
                  {question.metadata.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Problem Content - Full Height Scrollable */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="prose prose-sm max-w-none">
                <ReactMarkdown
                  components={{
                    code({ className, children, ...props }: any) {
                      const match = /language-(\w+)/.exec(className || "");
                      const inline = !match;
                      return !inline && match ? (
                        <SyntaxHighlighter
                          style={vscDarkPlus}
                          language={match[1]}
                          PreTag="div"
                          {...props}
                        >
                          {String(children).replace(/\n$/, "")}
                        </SyntaxHighlighter>
                      ) : (
                        <code className={className} {...props}>
                          {children}
                        </code>
                      );
                    },
                  }}
                >
                  {question.text}
                </ReactMarkdown>
              </div>

              {/* Evaluation Criteria */}
              {question.evaluationCriteria && (
                <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h3 className="text-sm font-semibold text-blue-900 mb-3">Evaluation Criteria</h3>
                  <div className="space-y-2 text-sm text-blue-800">
                    <div>
                      <strong>Time Complexity:</strong> {question.evaluationCriteria.timeComplexity}
                    </div>
                    <div>
                      <strong>Space Complexity:</strong>{" "}
                      {question.evaluationCriteria.spaceComplexity}
                    </div>
                    {question.evaluationCriteria.constraints.length > 0 && (
                      <div>
                        <strong>Constraints:</strong>
                        <ul className="list-disc list-inside mt-1 ml-2">
                          {question.evaluationCriteria.constraints.map((constraint, index) => (
                            <li key={index}>{constraint}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Examples Section */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4 text-gray-900">Examples</h3>
                <div className="space-y-4">
                  {question.testCases.slice(0, 2).map((testCase, index) => (
                    <div key={index} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <div className="font-semibold text-sm text-gray-900 mb-3">
                        Example {index + 1}
                      </div>
                      <div className="space-y-3 text-sm">
                        <div>
                          <div className="font-medium text-gray-700 mb-1">Input:</div>
                          <code className="block bg-white px-3 py-2 rounded text-xs font-mono border">
                            {testCase.input}
                          </code>
                        </div>
                        <div>
                          <div className="font-medium text-gray-700 mb-1">Output:</div>
                          <code className="block bg-white px-3 py-2 rounded text-xs font-mono border">
                            {testCase.expected_output}
                          </code>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Code Editor and Tabs */}
        <div className="w-1/2 flex flex-col">
          {/* Code Editor Header */}
          <div className="p-4 border-b border-border bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-gray-700">Code</span>
                <Badge
                  variant="outline"
                  className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                >
                  {question.language.charAt(0).toUpperCase() + question.language.slice(1)}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRunCode}
                  disabled={isRunning}
                  className="gap-2 bg-white hover:bg-gray-50"
                >
                  <Play className="h-4 w-4" />
                  {isRunning ? "Running..." : "Run"}
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveAndReturn}
                  disabled={isSaving}
                  className="gap-2 bg-green-600 hover:bg-green-700"
                >
                  <Save className="h-4 w-4" />
                  {isSaving ? "Saving..." : "Submit"}
                </Button>
              </div>
            </div>
          </div>

          {/* Code Editor */}
          <div className="min-h-96">
            <MonacoCodeEditor
              title={question.title}
              language={question.language}
              value={code}
              onChange={setCode}
              height="400px"
            />
          </div>

          {/* Bottom Tabs - Test Cases and Console */}
          <div className="border-t border-border">
            <Tabs defaultValue="testcases" className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-2 rounded border-b">
                <TabsTrigger value="testcases" className="rounded">
                  Test Cases
                  {testResults.length > 0 && (
                    <Badge
                      variant={passedTests === totalTests ? "default" : "destructive"}
                      className="ml-2 text-xs"
                    >
                      {passedTests}/{totalTests}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="console" className="rounded">
                  Console
                </TabsTrigger>
              </TabsList>

              <TabsContent value="testcases" className="flex-1 m-0 p-0">
                <div className="h-64 overflow-y-auto">
                  <Tabs defaultValue="case-0" className="h-full flex flex-col">
                    <TabsList className="w-full justify-start rounded-none border-b bg-gray-50 p-1">
                      {question.testCases.map((_, index) => {
                        const result = testResults[index];
                        const hasResult = result !== undefined;
                        const passed = hasResult ? result.passed : null;

                        return (
                          <TabsTrigger
                            key={index}
                            value={`case-${index}`}
                            ignoreActiveText={true}
                            className={`rounded-sm text-xs ${
                              !hasResult
                                ? "text-gray-600"
                                : passed
                                  ? "text-green-600 bg-green-50"
                                  : "text-red-600 bg-red-50"
                            }`}
                          >
                            Case {index + 1}
                            {hasResult && <span className="ml-1">{passed ? "✓" : "✗"}</span>}
                          </TabsTrigger>
                        );
                      })}
                    </TabsList>

                    {question.testCases.map((testCase, index) => {
                      const result = testResults[index];
                      const hasResult = result !== undefined;
                      const passed = hasResult ? result.passed : null;

                      return (
                        <TabsContent
                          key={index}
                          value={`case-${index}`}
                          className="flex-1 m-0 p-2 overflow-y-auto"
                        >
                          <div className="space-y-2">
                            <div>
                              <div className="text-sm font-medium text-gray-700 mb-2">Input:</div>
                              <code className="block bg-gray-100 px-2 py-1 rounded text-xs font-mono border">
                                {testCase.input}
                              </code>
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-700 mb-1">
                                Expected Output:
                              </div>
                              <code className="block bg-gray-100 px-2 py-1 rounded text-xs font-mono border">
                                {testCase.expected_output}
                              </code>
                            </div>
                            {hasResult && result.actualOutput && (
                              <div>
                                <div className="text-sm font-medium text-gray-700 mb-1">
                                  Your Output:
                                </div>
                                <code
                                  className={`block px-2 py-1 rounded text-xs font-mono border ${
                                    passed
                                      ? "bg-green-100 text-green-800 border-green-200"
                                      : "bg-red-100 text-red-800 border-red-200"
                                  }`}
                                >
                                  {result.actualOutput}
                                </code>
                              </div>
                            )}
                            {hasResult && (
                              <div
                                className={`text-sm font-medium ${passed ? "text-green-600" : "text-red-600"}`}
                              >
                                {passed ? "✓ Test Passed" : "✗ Test Failed"}
                              </div>
                            )}
                          </div>
                        </TabsContent>
                      );
                    })}
                  </Tabs>
                </div>
              </TabsContent>

              <TabsContent value="console" className="flex-1 m-0 p-0">
                <div className="h-64 bg-gray-900 text-gray-100 font-mono text-sm overflow-y-auto">
                  <div className="p-4">
                    <pre className="whitespace-pre-wrap">
                      {output || (
                        <span className="text-gray-400">
                          Click 'Run' to execute your code and see the output here...
                        </span>
                      )}
                    </pre>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodingDashboard;
