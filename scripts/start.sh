# Start Docker containers
echo "🛢️ Starting Docker containers..."
docker start skillify-db && docker start assessment-mongodb

# Start analysis server
echo "📊 Starting analysis server..."
(cd analysis-server && ./scripts/start_api_server.sh) &

# Start Spring Boot server
echo "🌱 Starting Spring Boot server..."
(cd skillify.io && mvn spring-boot:run) &

# Start frontend (g-client)
echo "💻 Starting g-client frontend..."
(cd g-client && npm run dev) &

# Optional: wait for all background processes
wait
