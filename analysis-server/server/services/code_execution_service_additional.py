"""
Additional test runners for the code execution service.
"""
import os
import json
import logging

logger = logging.getLogger(__name__)

def _write_go_test_runner(self, temp_dir: str, code_file_name: str) -> str:
    """Write a Go test runner script.

    Args:
        temp_dir: The temporary directory
        code_file_name: The name of the code file

    Returns:
        Path to the test runner script
    """
    # Create a Go module
    module_path = os.path.join(temp_dir, "go.mod")
    with open(module_path, "w") as f:
        f.write("module solution\n\ngo 1.18\n")

    # Create a main.go file that will run the tests
    runner_path = os.path.join(temp_dir, "main.go")

    # Write a Go test runner that can execute user code
    with open(runner_path, "w") as f:
        f.write(f"""
package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"
)

func main() {{
	// Read test cases from file
	testCasesBytes, err := ioutil.ReadFile("test_cases.json")
	if err != nil {{
		fmt.Println("Error reading test cases file:", err)
		os.Exit(1)
	}}

	// Parse test cases
	var testCases []map[string]interface{{}}
	err = json.Unmarshal(testCasesBytes, &testCases)
	if err != nil {{
		fmt.Println("Error parsing test cases:", err)
		os.Exit(1)
	}}

	// Read and execute the solution code
	solutionBytes, err := ioutil.ReadFile("{code_file_name}")
	if err != nil {{
		fmt.Println("Error reading solution file:", err)
		os.Exit(1)
	}}

	solutionCode := string(solutionBytes)
	fmt.Printf("Loaded solution code: %s\\n", solutionCode)

	// Run tests
	results := make([]map[string]interface{{}}, 0, len(testCases))

	for i, tc := range testCases {{
		testID := fmt.Sprintf("test_%d", i)

		// Get test case data
		input := tc["input"]
		expectedOutput := tc["expected_output"]
		functionName, _ := tc["function_name"].(string)

		fmt.Printf("Running test %s with input: %v, expected: %v\\n", testID, input, expectedOutput)

		// Execute the function
		var result interface{{}}
		var errorMsg *string

		startTime := time.Now()

		// For Go, we'll create a simple evaluation system
		// This is a simplified approach - in a real implementation, you'd want to
		// compile and execute the Go code properly
		actualOutput, err := executeGoFunction(solutionCode, functionName, input)
		if err != nil {{
			msg := err.Error()
			errorMsg = &msg
			result = ""
		}} else {{
			result = actualOutput
		}}

		executionTime := float64(time.Since(startTime).Microseconds()) / 1000.0 // Convert to ms

		// Convert result to string for comparison
		var actualOutputStr string
		if errorMsg == nil {{
			actualOutputStr = fmt.Sprintf("%v", result)
		}} else {{
			actualOutputStr = ""
		}}

		// Check if the output matches the expected output
		expectedOutputStr := fmt.Sprintf("%v", expectedOutput)
		passed := errorMsg == nil && actualOutputStr == expectedOutputStr

		fmt.Printf("Test %s: Expected=%s, Actual=%s, Passed=%t\\n", testID, expectedOutputStr, actualOutputStr, passed)

		// Add the result
		resultMap := map[string]interface{{}}{{
			"test_case_id":     testID,
			"passed":           passed,
			"actual_output":    actualOutputStr,
			"expected_output":  expectedOutput,
			"execution_time":   executionTime,
			"memory_usage":     0.0, // Memory profiling not implemented
			"error_message":    errorMsg,
		}}
		results = append(results, resultMap)
	}}

	// Write results to file
	resultsBytes, err := json.MarshalIndent(results, "", "  ")
	if err != nil {{
		fmt.Println("Error marshaling results:", err)
		os.Exit(1)
	}}

	err = ioutil.WriteFile("results.json", resultsBytes, 0644)
	if err != nil {{
		fmt.Println("Error writing results file:", err)
		os.Exit(1)
	}}

	fmt.Printf("Test execution complete. Results written to results.json\\n")
}}

// Simple function to execute Go functions (simplified approach)
func executeGoFunction(code, functionName string, input interface{{}}) (interface{{}}, error) {{
	// This is a very simplified approach for demonstration
	// In a real implementation, you would compile and execute the Go code properly

	// For now, we'll return an error indicating Go execution needs proper implementation
	return nil, fmt.Errorf("Go code execution requires proper compilation and execution - not fully implemented in this simplified version")
}}
""")

    # Create a solution.go file that imports the user's code
    solution_path = os.path.join(temp_dir, "solution.go")
    with open(solution_path, "w") as f:
        f.write("""
package main

// Import the user's code
""" + f"// {code_file_name} should be in the same directory")

    return runner_path

def _write_ruby_test_runner(self, temp_dir: str, code_file_name: str) -> str:
    """Write a Ruby test runner script.

    Args:
        temp_dir: The temporary directory
        code_file_name: The name of the code file

    Returns:
        Path to the test runner script
    """
    runner_path = os.path.join(temp_dir, "run_tests.rb")

    # Create a Ruby test runner script that can execute user code
    with open(runner_path, "w") as f:
        f.write(f"""
require 'json'

# Load test cases
begin
  test_cases = JSON.parse(File.read('test_cases.json'))
  puts "Loaded #{{test_cases.length}} test cases"
rescue => e
  puts "Error loading test cases: #{{e}}"
  error_results = [{{
    'test_case_id' => 'error',
    'passed' => false,
    'actual_output' => '',
    'expected_output' => '',
    'execution_time' => 0.0,
    'error_message' => "Error loading test cases: #{{e}}"
  }}]
  File.write('results.json', JSON.pretty_generate(error_results))
  exit 1
end

# Load the solution code
begin
  puts "Loading solution from {code_file_name}"
  require_relative './{os.path.splitext(code_file_name)[0]}'
  puts "Successfully loaded solution"
rescue => e
  puts "Error loading solution: #{{e}}"
  error_results = [{{
    'test_case_id' => 'error',
    'passed' => false,
    'actual_output' => '',
    'expected_output' => '',
    'execution_time' => 0.0,
    'error_message' => "Error loading solution: #{{e}}"
  }}]
  File.write('results.json', JSON.pretty_generate(error_results))
  exit 1
end

# Get all methods defined in the main object (solution methods)
solution_methods = Object.private_methods(false) + Object.methods(false) - Object.new.methods
solution_methods = solution_methods.select {{ |m| method(m).arity != 0 || method(m).arity == -1 }}

puts "Found solution methods: #{{solution_methods.join(', ')}}"

results = []

test_cases.each_with_index do |test_case, i|
  test_id = "test_#{{i}}"
  input_value = test_case['input'] || ""
  expected_output = test_case['expected_output'] || ""
  function_name = test_case['function_name'] || ""

  puts "Running test #{{test_id}} with input: #{{input_value.inspect}}, expected: #{{expected_output.inspect}}"

  # Find the method to test
  method_to_test = nil

  if !function_name.empty? && respond_to?(function_name)
    method_to_test = method(function_name)
    puts "Using method #{{function_name}} specified in test case"
  elsif solution_methods.length == 1
    method_to_test = method(solution_methods.first)
    puts "Using the only available method: #{{solution_methods.first}}"
  elsif solution_methods.length > 0
    # Default to the first method
    method_to_test = method(solution_methods.first)
    puts "Defaulting to first method: #{{solution_methods.first}}"
  end

  if method_to_test.nil?
    puts "No suitable method found"
    results << {{
      'test_case_id' => test_id,
      'passed' => false,
      'actual_output' => "",
      'expected_output' => expected_output,
      'execution_time' => 0.0,
      'error_message' => "No suitable method found"
    }}
    next
  end

  begin
    # Parse input based on type
    parsed_input = input_value
    if input_value.is_a?(String) && input_value.start_with?('[') && input_value.end_with?(']')
      begin
        parsed_input = JSON.parse(input_value)
        puts "Parsed input as array: #{{parsed_input.inspect}}"
      rescue JSON::ParserError => e
        puts "Failed to parse input as array, using as string: #{{e.message}}"
      end
    elsif input_value.is_a?(String) && (input_value.start_with?('{{') || input_value.match?(/^\\d+$/))
      begin
        parsed_input = JSON.parse(input_value)
        puts "Parsed input as JSON: #{{parsed_input.inspect}}"
      rescue JSON::ParserError => e
        puts "Failed to parse input as JSON, using as string: #{{e.message}}"
      end
    end

    # Execute the method
    puts "Executing method with input: #{{parsed_input.inspect}}"
    start_time = Time.now

    result = if parsed_input.is_a?(Array) && method_to_test.arity != 1
               # Pass each element as a separate argument
               puts "Passing array elements as separate arguments"
               method_to_test.call(*parsed_input)
             else
               # Pass as single argument
               puts "Passing input as single argument"
               method_to_test.call(parsed_input)
             end

    end_time = Time.now
    execution_time = (end_time - start_time) * 1000 # Convert to ms

    # Convert result for comparison
    actual_output = if result.is_a?(Array) || result.is_a?(Hash)
                      result.to_json
                    else
                      result.to_s
                    end

    puts "Method returned: #{{actual_output}}"

    # Check if the output matches the expected output
    expected_output_str = if expected_output.is_a?(Array) || expected_output.is_a?(Hash)
                            expected_output.to_json
                          else
                            expected_output.to_s
                          end

    passed = actual_output.strip == expected_output_str.strip
    puts "Test #{{passed ? 'PASSED' : 'FAILED'}} - Expected: #{{expected_output_str}}, Actual: #{{actual_output}}"

    results << {{
      'test_case_id' => test_id,
      'passed' => passed,
      'actual_output' => actual_output,
      'expected_output' => expected_output,
      'execution_time' => execution_time,
      'memory_usage' => 0.0,
      'error_message' => nil
    }}
  rescue => e
    puts "Error executing method: #{{e}}"
    results << {{
      'test_case_id' => test_id,
      'passed' => false,
      'actual_output' => "",
      'expected_output' => expected_output,
      'execution_time' => 0.0,
      'memory_usage' => 0.0,
      'error_message' => "#{{e}}\\n#{{e.backtrace.join('\\n')}}"
    }}
  end
end

# Write results to file
puts "Writing #{{results.length}} results to file"
File.write('results.json', JSON.pretty_generate(results))
puts "Test execution complete"
""")

    # Create a file to load the solution
    solution_loader_path = os.path.join(temp_dir, "solution_loader.rb")
    with open(solution_loader_path, "w") as f:
        f.write(f"""
# This file will load the solution
require_relative './{os.path.splitext(code_file_name)[0]}'
""")

    return runner_path

def _write_cpp_test_runner(self, temp_dir: str, code_file_name: str) -> str:
    """Write a C++ test runner script.

    Args:
        temp_dir: The temporary directory
        code_file_name: The name of the code file

    Returns:
        Path to the test runner script
    """
    # Create a C++ test runner that can compile and execute user code
    runner_path = os.path.join(temp_dir, "test_runner.cpp")

    with open(runner_path, "w") as f:
        f.write(f"""
#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <sstream>
#include <chrono>
#include <nlohmann/json.hpp>

using json = nlohmann::json;
using namespace std;

// Include the solution code
#include "{code_file_name}"

int main() {{
    try {{
        cout << "Starting C++ test runner" << endl;

        // Read test cases from file
        ifstream test_cases_file("test_cases.json");
        if (!test_cases_file.is_open()) {{
            cerr << "Error opening test cases file" << endl;
            return 1;
        }}

        json test_cases_json;
        test_cases_file >> test_cases_json;
        test_cases_file.close();

        cout << "Loaded " << test_cases_json.size() << " test cases" << endl;

        // Create results array
        json results = json::array();

        // For each test case, create a result
        for (size_t i = 0; i < test_cases_json.size(); ++i) {{
            const auto& tc = test_cases_json[i];
            string test_id = "test_" + to_string(i);

            auto input = tc.value("input", "");
            auto expected_output = tc.value("expected_output", "");
            string function_name = tc.value("function_name", "");

            cout << "Running test " << test_id << " with input: " << input << ", expected: " << expected_output << endl;

            json result;
            result["test_case_id"] = test_id;
            result["expected_output"] = expected_output;
            result["memory_usage"] = 0.0;

            try {{
                auto start_time = chrono::high_resolution_clock::now();

                // This is a simplified approach - in a real implementation,
                // you would need to dynamically call the appropriate function
                // For now, we'll indicate that C++ execution needs proper implementation
                string actual_output = "";
                bool passed = false;
                string error_message = "C++ dynamic function execution requires proper compilation and linking - not fully implemented in this simplified version";

                auto end_time = chrono::high_resolution_clock::now();
                auto duration = chrono::duration_cast<chrono::milliseconds>(end_time - start_time);

                result["passed"] = passed;
                result["actual_output"] = actual_output;
                result["execution_time"] = duration.count();
                result["error_message"] = error_message;

                cout << "Test " << (passed ? "PASSED" : "FAILED") << endl;
            }} catch (const exception& e) {{
                cout << "Error executing test: " << e.what() << endl;
                result["passed"] = false;
                result["actual_output"] = "";
                result["execution_time"] = 0.0;
                result["error_message"] = string("Error executing test: ") + e.what();
            }}

            results.push_back(result);
        }}

        // Write results to file
        ofstream results_file("results.json");
        results_file << results.dump(4);
        results_file.close();

        cout << "Test execution complete. Results written to results.json" << endl;
        return 0;
    }} catch (const exception& e) {{
        cerr << "Error: " << e.what() << endl;

        // Create error result
        json error_results = json::array();
        json error_result;
        error_result["test_case_id"] = "error";
        error_result["passed"] = false;
        error_result["actual_output"] = "";
        error_result["expected_output"] = "";
        error_result["execution_time"] = 0.0;
        error_result["error_message"] = string("Unhandled exception: ") + e.what();
        error_results.push_back(error_result);

        ofstream results_file("results.json");
        results_file << error_results.dump(4);
        results_file.close();

        return 1;
    }}
}}
""")

    # Create a simple build script instead of CMake
    build_script_path = os.path.join(temp_dir, "build_and_run.sh")
    with open(build_script_path, "w") as f:
        f.write(f"""#!/bin/bash
set -e

echo "Building C++ test runner..."

# Download nlohmann/json header if not present
if [ ! -f "json.hpp" ]; then
    echo "Downloading nlohmann/json header..."
    wget -q -O json.hpp https://github.com/nlohmann/json/releases/download/v3.11.2/json.hpp
fi

# Compile the test runner
echo "Compiling test runner..."
g++ -std=c++17 -I. -o test_runner test_runner.cpp

# Run the test runner
echo "Running tests..."
./test_runner

echo "C++ test execution complete"
""")

    # Make the build script executable
    os.chmod(build_script_path, 0o755)

    # Create a Dockerfile for building and running the C++ code
    dockerfile_path = os.path.join(temp_dir, "Dockerfile")
    with open(dockerfile_path, "w") as f:
        f.write("""
FROM gcc:latest

WORKDIR /app

# Install wget for downloading dependencies
RUN apt-get update && apt-get install -y \
    wget \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy the source files
COPY . /app/

# Make build script executable
RUN chmod +x build_and_run.sh

# Run the build and test script
CMD ["./build_and_run.sh"]
""")

    return runner_path
