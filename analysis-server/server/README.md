# Assessment Analysis Server

A server for analyzing coding assessment solutions.

## Overview

The Assessment Analysis Server retrieves assessments and solutions from MongoDB, analyzes the solutions, and stores the analysis results back in MongoDB. It provides a comprehensive analysis of coding solutions, including:

- **Correctness**: Evaluates if the code passes test cases
- **Code Quality**: Analyzes maintainability, complexity, and documentation
- **AI Detection**: Detects if the code was likely generated by AI
- **Style Analysis**: Checks adherence to coding style and naming conventions
- **Performance Analysis**: Evaluates time and space complexity

## Installation

1. Clone the repository
2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Set up environment variables:

Create a `.env` file with the following variables:

```
MONGODB_URI=mongodb://localhost:27017/assessment_db
DATABASE_NAME=assessment_db
ASSESSMENTS_COLLECTION=assessments
SOLUTIONS_COLLECTION=solutions
ANALYSIS_COLLECTION=analysis
REPORTS_COLLECTION=reports
```

## Usage

### Process a specific test

```bash
python server.py --test-id <test_id> [--generate-reports]
```

### Process a specific solution

```bash
python server.py --solution-id <solution_id> [--generate-reports]
```

### Process all unprocessed solutions

```bash
python server.py --analyze-all [--generate-reports]
```

## Architecture

The server is organized into the following components:

- **Database Service**: Handles interactions with MongoDB
- **Analysis Service**: Coordinates the analysis of solutions
- **Reporting Service**: Generates reports based on analysis results
- **Ranking Service**: Ranks candidates based on their performance
- **Analyzers**: Individual analyzers for different aspects of code
  - Correctness Analyzer
  - Code Quality Analyzer
  - AI Detection Analyzer
  - Style Analyzer
  - Performance Analyzer
- **Transformers**: Transforms data between different formats
  - Solution Transformer

## Data Flow

1. The server retrieves assessments and solutions from MongoDB
2. Solutions are transformed into an analyzable format
3. Each solution is analyzed by multiple analyzers
4. Analysis results are stored back in MongoDB
5. Reports are generated based on the analysis results

## License

MIT
