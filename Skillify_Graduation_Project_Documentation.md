# SOFTWARE REQUIREMENTS

## SPECIFICATION (SRS)

## For

**SKILLIFY.IO - ASSESSMENT MANAGEMENT PLATFORM**

**by**

**[YOUR NAME] ([YOUR STUDENT ID])**

**Submission date: [DATE]**

# SKILLIFY.IO

## ASSESSMENT MANAGEMENT PLATFORM

**_Computer Science Graduation Project_**

**_Menoufia University, Egypt_**

## Table of Contents

- 1. INTRODUCTION
  - 1.1 PURPOSE
  - 1.2 SCOPE
  - 1.3 Definitions, acronyms, and abbreviations
  - 1.4 References
  - 1.5 Overview write in terms of chapters/sections
- 2. OVERALL DESCRIPTION
  - 2.1 Product Perspective
  - 2.2 PRODUCT FEATURES
  - 2.3 User Classes and Characteristics
  - 2.4 Operating Environment
  - 2.5 Design and Implementation Constraints
  - 2.6 User Documentation
  - 2.7 Assumptions and Dependencies
- 3. Use Case Diagram with Use-case descriptions
- Use case descriptions:
- 4. External Interface Requirements
  - 4.1 User Interfaces
  - 4.2 Hardware Interfaces
  - 4.3 Software Interfaces
  - 4.4 Communications Interfaces
- 5. Other Nonfunctional Requirements
  - 5.1 Performance Requirements
  - 5.2 Safety Requirements
  - 5.3 Security Requirements
  - 5.4 Software Quality Attributes
- 6. Other Requirements
- 7. DATA FLOW DIAGRAM
  - 7.1 LEVEL 0 DATA FLOW DIAGRAM
  - 7.2 LEVEL 1 DATA FLOW DIAGRAM
  - 7.3 LEVEL 2 DATA FLOW DIAGRAMS
- 8. Functional Requirements
- 9. Sequence Diagram
- 10. Class Diagrams
  - 10.1 Initial Class diagram
  - 10.2 Modified Class Diagram
  - 10.3 Detailed Class Diagram

**PART** - **I**

## 1. INTRODUCTION

This section discusses about the purpose, scope description and a short context about the various
topics in this SRS document. In addition to this, this section also describes a list of abbreviations
and definitions which will be used throughout the document.

### 1.1 PURPOSE

The purpose of this document is to give a detailed description of the requirements for the
"Skillify.io Assessment Management Platform" software. It will illustrate the purpose and complete declaration for
the development of system. It will also explain system constraints, interface and interactions with
other external applications. This document is primarily intended to be proposed to a customer for
its approval and serves as a graduation project documentation for Computer Science program at Menoufia University.

### 1.2 SCOPE

Skillify.io is a comprehensive web-based assessment management platform that allows organizations
to create, manage, and conduct technical assessments and coding interviews. This software is platform independent,
flexibly runs on all operating systems and in all modern web browsers. Skillify.io provides advanced
assessment creation capabilities with multiple question types including MCQ, open-ended, and coding questions.

The platform consists of three main components:

1. **Main Backend Server (skillify.io)**: Java Spring Boot application handling core business logic
2. **Analysis Server**: Python Flask service for AI-powered code analysis and reporting
3. **Frontend Client (g-client)**: Next.js React application providing user interface

Organizations can create assessments, invite candidates, and receive comprehensive analysis reports.
The system supports real-time code execution, AI-powered code quality analysis, plagiarism detection,
and automated scoring. Assessment creators can design custom coding problems or select from predefined
templates with various difficulty levels.

All assessment data, candidate responses, and analysis results are securely stored in PostgreSQL
and MongoDB databases. The platform generates detailed reports including individual candidate
performance, comparative analysis, and organizational insights. Corporate management can access
analytics dashboards to track assessment effectiveness and candidate performance trends.

Furthermore, the software requires Internet connection for real-time features and cloud-based
code execution. All system information such as user profiles, organization details, assessment
configurations, and candidate responses are maintained in secure databases with proper backup
and recovery mechanisms.

### 1.3 Definitions, acronyms, and abbreviations

```
TERM DEFINITION
AMS Assessment Management System
API Application Programming Interface
JWT JSON Web Token
MCQ Multiple Choice Question
SRS Software Requirements Specification
UI User Interface
UX User Experience
REST Representational State Transfer
CRUD Create, Read, Update, Delete
AI Artificial Intelligence
ML Machine Learning
IDE Integrated Development Environment
Admin/Administrator System administrator who is given specific permission for managing
and controlling the system
Organization Owner Someone who creates and manages an organization within the platform
HR Manager Human Resources manager with permissions to manage assessments
and candidates
Interviewer Someone who creates assessments and evaluates candidates
Candidate A person taking an assessment
Stakeholder Any person who has interaction with the system who is not a
developer
Assessment A collection of questions designed to evaluate candidate skills
Test Case A set of input/output pairs used to validate coding solutions
Code Analysis Automated evaluation of code quality, style, and correctness
```

### 1.4 References

[1] IEEE Software Engineering Standards Committee, "IEEE Std 830-1998, IEEE
Recommended Practice for Software Requirements Specifications", October 20, 1998.
[2] Spring Framework Documentation, "Spring Boot Reference Guide",
Link: https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/
[3] Next.js Documentation, "Next.js App Router",
Link: https://nextjs.org/docs
[4] Flask Documentation, "Flask Web Development",
Link: https://flask.palletsprojects.com/

### 1.5 OVERVIEW

This Software Requirements Specification document consists of 10 main sections. The second section
discusses about the product perspectives such as product features, various user classes, design and
implementation constraints.

The third section describes the use cases in detail and various use case descriptions for the same.
The fourth section discusses about the various user interfaces, hardware interfaces and software
interfaces required for the Skillify.io Assessment Management Platform.

The fifth and sixth sections explain the non-functional requirements and other requirements that
have to be considered while developing the AMS. The seventh section depicts the Data flow diagrams
for Skillify.io which mainly focuses on the data flow between various entities and processes.

The eighth section describes about the functional requirements in a detailed process in expected
Input, Output, Process and Error format. The last two sections explain the sequential flow of
various processes and the class diagrams of Skillify.io Assessment Management Platform.

## 2. OVERALL DESCRIPTION

### 2.1 Product Perspective

The Skillify.io Assessment Management Platform has evolved as a solution to achieve business
objectives and facilitate comprehensive technical assessments. The platform replaces traditional
manual and paper-based assessment processes with a modern, automated, and intelligent system.

The AMS mainly consists of three interconnected components: Frontend User Interface (Next.js),
Backend API Server (Spring Boot), and Analysis Server (Python Flask). Through the user interface,
organization owners, HR managers, interviewers, and candidates interact and update information
pertaining to assessments and evaluations.

A system administrator maintains all data regarding user profiles, organization profiles, assessment
configurations, and takes care of the overall platform maintenance. The analysis server provides
AI-powered code evaluation, quality assessment, and comprehensive reporting capabilities.

_Figure 2.1 Simple diagrammatic representation of Skillify.io Assessment Management Platform_

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Analysis      │
│   (Next.js)     │◄──►│   (Spring Boot) │◄──►│   (Python)      │
│   Port: 3001    │    │   Port: 8080    │    │   Port: 5000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   Database      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MongoDB       │
                    │   (Analysis)    │
                    └─────────────────┘
```

### 2.2 PRODUCT FEATURES

```
The following list offers a brief outline and description of the main features and functionalities of
the Skillify.io Assessment Management Platform. The features are split into two major categories: core features
and additional features. Core features are essential to the application's operation, whereas
additional features simply add new functionalities. The latter features will only be implemented as
time permits.
```

CORE FEATURES

1. ORGANIZATION AND USER MANAGEMENT:
   ♦ Organization owners can create and manage organizations
   ♦ Multi-level permission system with role-based access control
   ♦ User registration and authentication with JWT tokens
   ♦ Member invitation and role assignment within organizations
   ♦ Support for multiple organizations per user

2. JOB AND ASSESSMENT CREATION:
   ♦ HR managers can create job postings within organizations
   ♦ Interviewers can create assessments linked to specific jobs
   ♦ Support for multiple question types: MCQ, Open-ended, and Coding
   ♦ Drag-and-drop question ordering and management
   ♦ Assessment scheduling with start and end times
   ♦ Time limits and attempt restrictions

3. CANDIDATE INVITATION AND MANAGEMENT:
   ♦ Bulk candidate invitation via CSV/Excel file upload
   ♦ Email validation and duplicate detection
   ♦ Invitation status tracking (Pending, Sent, Completed, Expired)
   ♦ Automated email notifications and reminders
   ♦ Candidate progress monitoring

4. ASSESSMENT TAKING EXPERIENCE:
   ♦ Professional coding environment with Monaco Editor
   ♦ Real-time code execution and testing
   ♦ Support for multiple programming languages (Java, Python, JavaScript, etc.)
   ♦ Auto-save functionality and progress tracking
   ♦ Mobile-responsive design for various devices
   ♦ Assessment preview and submission confirmation

5. AI-POWERED CODE ANALYSIS:
   ♦ Automated code correctness evaluation against test cases
   ♦ Code quality analysis including complexity and maintainability
   ♦ AI-generated code detection and plagiarism checking
   ♦ Style analysis and coding convention adherence
   ♦ Performance analysis with time and space complexity estimation
   ♦ Detailed scoring and ranking algorithms

6. COMPREHENSIVE REPORTING AND ANALYTICS:
   ♦ Individual candidate performance reports
   ♦ Comparative analysis across all candidates
   ♦ Assessment effectiveness metrics and insights
   ♦ Real-time analytics dashboards
   ♦ Export capabilities (PDF, Excel, CSV)
   ♦ Trend analysis and performance tracking

7. SECURE CODE EXECUTION ENVIRONMENT:
   ♦ Docker-based isolated code execution
   ♦ Support for multiple programming languages
   ♦ Automatic test case generation and validation
   ♦ Resource limitation and timeout handling
   ♦ Security measures against malicious code

8. ASSESSMENT CONFIGURATION AND CUSTOMIZATION:
   ♦ Custom coding problem creation with test cases
   ♦ Predefined problem templates from major tech companies
   ♦ Difficulty level assignment and categorization
   ♦ Assessment templates and reusable components
   ♦ Batch operations for question management

ADDITIONAL FEATURES

9. ADVANCED ANALYTICS AND INSIGHTS:
   ♦ Machine learning-based candidate scoring
   ♦ Predictive performance modeling
   ♦ Custom report builders and visualizations
   ♦ Integration with external analytics tools

10. COLLABORATION AND COMMUNICATION:
    ♦ Real-time collaboration tools for assessment review
    ♦ Comment and feedback system for evaluators
    ♦ Team-based assessment creation and management
    ♦ Notification system for important events

11. INTEGRATION CAPABILITIES:
    ♦ REST API for third-party integrations
    ♦ Webhook support for external systems
    ♦ Single Sign-On (SSO) integration
    ♦ Calendar integration for assessment scheduling

12. MOBILE APPLICATION SUPPORT:
    ♦ Progressive Web App (PWA) capabilities
    ♦ Mobile-optimized assessment interface
    ♦ Offline capability for certain features
    ♦ Push notifications for mobile devices

### 2.3 User Classes and Characteristics

The various user classes for Skillify.io Assessment Management Platform are as follows:

ORGANIZATION OWNER

- An Organization Owner is a user who creates and manages an organization within the platform
- They have full administrative privileges within their organization
- Can invite members, assign roles, and manage organizational settings
- Responsible for subscription management and billing
- Can create multiple organizations and manage them independently
- All organizational data and member activities are accessible to the organization owner

SYSTEM ADMINISTRATOR

- System Administrators have global access to the entire platform
- Responsible for platform maintenance, user support, and system monitoring
- Can access all organizations and user data for support purposes
- Manages platform-wide settings, security configurations, and system updates
- Monitors system performance and handles technical issues

HR MANAGER

- HR Managers are organization members with permissions to manage recruitment processes
- Can create and manage job postings within their organization
- Responsible for candidate invitation and communication
- Can view assessment results and candidate performance reports
- Manages the overall recruitment workflow and candidate experience
- Works closely with interviewers to coordinate assessment processes

INTERVIEWER

- Interviewers are technical team members responsible for creating and managing assessments
- Can create various types of questions including coding, MCQ, and open-ended
- Responsible for assessment configuration, test case creation, and evaluation criteria
- Can review candidate submissions and provide feedback
- Has access to detailed analysis reports and candidate performance metrics
- Can collaborate with other interviewers on assessment creation and evaluation

CANDIDATE

- Candidates are external users who take assessments through invitation links
- Have limited access to only their assigned assessments
- Can view assessment instructions, take tests, and submit responses
- Access to professional coding environment for programming questions
- Can track their assessment progress and submission status
- Receive automated notifications about assessment deadlines and results

GUEST USER

- Guest users can access public pages and general platform information
- Can view platform features, pricing, and documentation
- Limited to read-only access without assessment capabilities
- Can register to become organization owners or request access to existing organizations

### 2.4 Operating Environment

**OPERATING
ENVIRONMENT**

```
DESCRIPTION
```

**_Web Browser
Support_**

_The Skillify.io platform shall operate correctly with the following web browsers:
Google Chrome (version 90+), Mozilla Firefox (version 88+), Safari (version 14+),
and Microsoft Edge (version 90+). The platform is optimized for modern browsers
with ES6+ support._

**_Server Environment_** \_The platform shall operate on cloud infrastructure supporting:

- Linux-based servers (Ubuntu 20.04 LTS or CentOS 8+)
- Docker containerization support
- Load balancing capabilities
- SSL/TLS encryption support\_

**_Database Environment_** \_The system requires:

- PostgreSQL 13+ for primary application data
- MongoDB 5.0+ for analysis and reporting data
- Redis for caching and session management
- Backup and recovery mechanisms\_

**_Mobile Platform_** \_The platform provides responsive web interface supporting:

- iOS Safari (version 14+)
- Android Chrome (version 90+)
- Progressive Web App (PWA) capabilities
- Touch-optimized interface elements\_

### 2.5 Design and Implementation Constraints

**CONSTRAINTS NUMBER DESCRIPTION**
**_Constraint 1_** _The platform's backend shall be developed using Java Spring Boot
framework, ensuring enterprise-grade scalability and maintainability._
**_Constraint 2_** _The frontend shall be built using Next.js with React, providing
modern user experience and server-side rendering capabilities._
**_Constraint 3_** _The analysis server shall use Python Flask for AI-powered code
analysis and machine learning capabilities._
**_Constraint 4_** _The system shall use PostgreSQL as primary database and MongoDB
for analysis data storage._
**_Constraint 5_** _All code execution shall be performed in isolated Docker containers
for security and resource management._
**_Constraint 6_** _The platform shall implement JWT-based authentication with
role-based access control._
**_Constraint 7_** _All API endpoints shall follow RESTful design principles with
comprehensive Swagger documentation._

### 2.6 User Documentation

This section elaborates the user manual and tutorials for various user classes.

User Manual:

```
User manual for Registration and Organization Setup in Skillify.io Assessment Platform
```

Step 1: Navigate to Skillify.io platform URL in your web browser

Step 2: Click "Sign Up" and register with your email credentials or Google account

Step 3: After successful registration, verify your email address

Step 4: Create your first organization by providing organization name and details

Step 5: Set up your organization profile and invite team members

Step 6: Configure organization settings and permissions

Tutorials:

```
Tutorial for Creating an Assessment:
```

Step 1: Login to your organization dashboard

Step 2: Navigate to "Jobs" section and create a new job posting

Step 3: Click "Create Assessment" for the specific job

Step 4: Add questions using the question builder (MCQ, Open-ended, or Coding)

Step 5: Configure assessment settings (time limits, scheduling, etc.)

Step 6: Preview the assessment and save

Step 7: Generate invitation links or upload candidate list

Step 8: Send invitations to candidates

```
Tutorial for Taking an Assessment (Candidate):
```

Step 1: Click on the assessment invitation link received via email

Step 2: Read assessment instructions and time limits

Step 3: Start the assessment when ready

Step 4: Answer MCQ and open-ended questions

Step 5: For coding questions, use the integrated code editor

Step 6: Test your code using the provided test cases

Step 7: Submit individual answers and final assessment

Step 8: Receive confirmation of successful submission

```
Tutorial for Reviewing Assessment Results:
```

Step 1: Navigate to the assessment dashboard

Step 2: Select the completed assessment

Step 3: View candidate responses and analytics

Step 4: Review AI-generated analysis reports

Step 5: Compare candidate performances

Step 6: Export reports in desired format (PDF, Excel)

### 2.7 Assumptions and Dependencies

Assumptions:

1. Organizations have reliable internet connectivity for platform access
2. Candidates have access to modern web browsers supporting JavaScript
3. Users have basic computer literacy and familiarity with web applications
4. Organizations comply with data protection and privacy regulations

Dependencies:

1. The platform is highly dependent on internet connectivity for real-time features
2. Code execution depends on Docker infrastructure availability
3. Email delivery depends on configured SMTP services
4. Analysis features depend on Python machine learning libraries
5. Database performance depends on proper indexing and optimization
6. Security depends on proper SSL/TLS certificate configuration

## 3. Use Case Diagram with Use-case descriptions

USE CASE DIAGRAM:

_[Use Case Diagram would be inserted here showing the relationships between different actors and use cases]_

## Use case descriptions:

**Use Case ID:** UC-1
**Use Case Name:** Register and Create Organization
**Created By:** [Your Name] **Last Updated By:** [Your Name]
**Date Created:** [Date] **Last Revision Date:** [Date]
**Actors:** Organization Owner
**Description:** This use case helps users to register with Skillify.io platform and create their organization.
**Trigger:** User wants to start using the assessment platform for their organization's hiring needs.
**Preconditions:** 1. User shall have a valid email address 2. User shall have internet connectivity
**Postconditions:** User is registered and has created an organization with admin privileges.
**Normal Flow:** 1.0 User navigates to Skillify.io platform
2.0 User clicks "Sign Up" button
3.0 User provides email, password, and organization details
4.0 System sends verification email to user
5.0 User verifies email address
6.0 System creates user account and organization
7.0 User is redirected to organization dashboard
**Exceptions:** 2.0.E.1 Invalid email format
System displays error message for invalid email
3.0.E.1 Email already exists
System displays error message if email is already registered
**Includes:** User has valid email address and internet connection
**Frequency of Use:** Approximately 10-20 new organizations per day
**Assumptions:** User understands English and has basic computer literacy
**Priority:** High

**Use Case ID:** UC-2
**Use Case Name:** Create Assessment
**Created By:** [Your Name] **Last Updated By:** [Your Name]
**Date Created:** [Date] **Last Revision Date:** [Date]
**Actors:** Interviewer, HR Manager
**Description:** This use case helps authorized users create assessments for candidate evaluation.
**Trigger:** User needs to create an assessment for a specific job position.
**Preconditions:** 1. User is logged in and has appropriate permissions 2. Job posting exists in the system
**Postconditions:** Assessment is created and ready for candidate invitations.
**Normal Flow:** 1.0 User navigates to job dashboard
2.0 User selects "Create Assessment" for specific job
3.0 User configures assessment settings (time limit, scheduling)
4.0 User adds questions (MCQ, Open-ended, Coding)
5.0 User configures test cases for coding questions
6.0 User previews assessment
7.0 User saves and publishes assessment
**Exceptions:** 4.0.E.1 Invalid question format
System displays error for improperly formatted questions
5.0.E.1 Missing test cases
System requires test cases for coding questions
**Includes:** User has permission to create assessments
**Frequency of Use:** Approximately 50-100 assessments created per day
**Assumptions:** User understands assessment creation process
**Priority:** High

**Use Case ID:** UC-3
**Use Case Name:** Take Assessment
**Created By:** [Your Name] **Last Updated By:** [Your Name]
**Date Created:** [Date] **Last Revision Date:** [Date]
**Actors:** Candidate
**Description:** This use case allows candidates to take assessments through invitation links.
**Trigger:** Candidate receives assessment invitation and wants to complete the test.
**Preconditions:** 1. Candidate has received valid invitation link 2. Assessment is active and within time limits
**Postconditions:** Candidate completes assessment and responses are submitted for analysis.
**Normal Flow:** 1.0 Candidate clicks invitation link
2.0 System displays assessment instructions
3.0 Candidate starts assessment
4.0 Candidate answers MCQ and open-ended questions
5.0 Candidate solves coding problems using code editor
6.0 Candidate tests code against provided test cases
7.0 Candidate submits assessment
8.0 System confirms successful submission
**Exceptions:** 2.0.E.1 Assessment expired
System displays expiration message
5.0.E.1 Code compilation error
System displays compilation errors to candidate
**Includes:** Candidate has access to modern web browser
**Frequency of Use:** Approximately 200-500 assessments taken per day
**Assumptions:** Candidate has basic programming knowledge for coding questions
**Priority:** High

**Use Case ID:** UC-4
**Use Case Name:** Analyze Assessment Results
**Created By:** [Your Name] **Last Updated By:** [Your Name]
**Date Created:** [Date] **Last Revision Date:** [Date]
**Actors:** System (Analysis Server)
**Description:** This automated use case analyzes candidate submissions using AI-powered algorithms.
**Trigger:** Candidate submits assessment or manual analysis is requested.
**Preconditions:** 1. Assessment submission exists in database 2. Analysis server is operational
**Postconditions:** Comprehensive analysis report is generated and stored.
**Normal Flow:** 1.0 System receives analysis request
2.0 Analysis server retrieves candidate submission
3.0 System performs correctness analysis against test cases
4.0 System analyzes code quality and complexity
5.0 System performs AI detection analysis
6.0 System evaluates coding style and conventions
7.0 System generates comprehensive report
8.0 System stores analysis results in database
**Exceptions:** 3.0.E.1 Code execution timeout
System handles timeout and marks as execution error
4.0.E.1 Analysis service unavailable
System queues request for later processing
**Includes:** Docker environment for secure code execution
**Frequency of Use:** Continuous automated processing
**Assumptions:** Analysis algorithms are properly configured
**Priority:** High

## 4. External Interface Requirements

### 4.1 User Interfaces

The Skillify.io platform provides multiple user interfaces designed for different user types:

**Organization Dashboard Interface:**

- Clean, modern design with responsive layout
- Navigation sidebar with role-based menu items
- Real-time statistics and analytics widgets
- Drag-and-drop functionality for assessment creation
- Dark/light theme support

**Assessment Taking Interface:**

- Minimalist design to reduce distractions
- Professional code editor with syntax highlighting
- Split-pane layout for problem description and solution
- Real-time test execution results
- Progress indicators and time remaining display

**Mobile Interface:**

- Responsive design for tablets and smartphones
- Touch-optimized controls and navigation
- Progressive Web App (PWA) capabilities
- Offline support for certain features

### 4.2 Hardware Interfaces

The platform operates entirely through web browsers and does not require specific hardware interfaces. However, it supports:

- Standard computer peripherals (keyboard, mouse, touchpad)
- Touch screen devices (tablets, smartphones)
- High-resolution displays for optimal code editing experience
- Webcam and microphone for future video interview features

### 4.3 Software Interfaces

**Database Interfaces:**

- PostgreSQL database for primary application data
- MongoDB for analysis results and reporting data
- Redis for caching and session management

**External Service Interfaces:**

- SMTP servers for email notifications
- Docker Engine for secure code execution
- Cloud storage services for file uploads
- Third-party authentication providers (Google OAuth)

**API Interfaces:**

- RESTful APIs for frontend-backend communication
- Analysis server APIs for code evaluation
- Webhook endpoints for external integrations

### 4.4 Communications Interfaces

**Network Protocols:**

- HTTPS for secure web communication
- WebSocket for real-time features
- SMTP for email delivery
- REST over HTTP for API communication

**Data Formats:**

- JSON for API data exchange
- CSV/Excel for bulk data import/export
- PDF for report generation
- Base64 encoding for file uploads

## 5. Other Nonfunctional Requirements

### 5.1 Performance Requirements

**Response Time:**

- Web page loading: < 3 seconds
- API response time: < 500ms for standard operations
- Code execution: < 30 seconds per test case
- Report generation: < 60 seconds for standard reports

**Throughput:**

- Support 1000+ concurrent users
- Handle 10,000+ assessments per day
- Process 100+ code submissions simultaneously
- Generate 500+ reports per hour

**Scalability:**

- Horizontal scaling capability for increased load
- Database partitioning for large datasets
- Load balancing across multiple server instances
- Auto-scaling based on demand

### 5.2 Safety Requirements

**Data Backup:**

- Automated daily database backups
- Real-time data replication
- Point-in-time recovery capabilities
- Disaster recovery procedures

**System Monitoring:**

- 24/7 system health monitoring
- Automated alerting for critical issues
- Performance metrics tracking
- Error logging and analysis

### 5.3 Security Requirements

**Authentication:**

- Strong password requirements
- JWT-based session management
- Multi-factor authentication support
- Account lockout after failed attempts

**Authorization:**

- Role-based access control (RBAC)
- Resource-level permissions
- Organization data isolation
- API endpoint security

**Data Protection:**

- Encryption at rest and in transit
- GDPR compliance for data privacy
- Secure code execution environment
- Regular security audits and updates

### 5.4 Software Quality Attributes

**Reliability:**

- 99.9% uptime availability
- Graceful error handling
- Automatic failover mechanisms
- Data consistency guarantees

**Usability:**

- Intuitive user interface design
- Comprehensive help documentation
- Accessibility compliance (WCAG 2.1)
- Multi-language support capability

**Maintainability:**

- Modular architecture design
- Comprehensive code documentation
- Automated testing coverage
- Version control and deployment automation

## 6. Other Requirements

**Legal and Compliance:**

- GDPR compliance for European users
- SOC 2 Type II certification
- Data residency requirements
- Terms of service and privacy policy

**Internationalization:**

- Unicode support for multiple languages
- Timezone handling for global users
- Currency support for pricing
- Localized date and time formats

**Integration Requirements:**

- REST API for third-party integrations
- Webhook support for external notifications
- Single Sign-On (SSO) capabilities
- Import/export functionality for data migration

## 7. DATA FLOW DIAGRAM

### 7.1 LEVEL 0 DATA FLOW DIAGRAM

_[Level 0 DFD would be inserted here showing the overall system context with external entities, main process, and data stores]_

**External Entities:**

- Organization Owners
- HR Managers
- Interviewers
- Candidates
- System Administrators

**Main Process:** Skillify.io Assessment Management Platform

**Data Stores:**

- User Database
- Assessment Database
- Analysis Database
- Report Database

### 7.2 LEVEL 1 DATA FLOW DIAGRAM

_[Level 1 DFD would be inserted here showing the decomposition of the main process into major subsystems]_

**Major Processes:**

1. User Management Process
2. Organization Management Process
3. Assessment Creation Process
4. Assessment Taking Process
5. Analysis Process
6. Reporting Process

### 7.3 LEVEL 2 DATA FLOW DIAGRAMS

_[Level 2 DFDs would be inserted here showing detailed breakdown of each major process]_

**Assessment Creation Process (Detailed):**

- Question Creation
- Test Case Configuration
- Assessment Settings
- Preview and Validation
- Publishing

**Analysis Process (Detailed):**

- Code Execution
- Quality Analysis
- AI Detection
- Performance Evaluation
- Report Generation

## 8. Functional Requirements

**FR-1: User Registration and Authentication**

- Input: User credentials, organization details
- Process: Validate input, create user account, send verification email
- Output: User account created, verification email sent
- Error: Invalid email format, duplicate email, verification failure

**FR-2: Assessment Creation**

- Input: Assessment details, questions, test cases
- Process: Validate questions, configure settings, save assessment
- Output: Assessment created and ready for use
- Error: Invalid question format, missing test cases, permission denied

**FR-3: Candidate Invitation**

- Input: Candidate email list, assessment ID
- Process: Validate emails, generate invitation links, send emails
- Output: Invitations sent successfully
- Error: Invalid email format, assessment not found, email delivery failure

**FR-4: Assessment Taking**

- Input: Candidate responses, code submissions
- Process: Validate responses, execute code, save submissions
- Output: Assessment completed, confirmation sent
- Error: Time limit exceeded, compilation error, submission failure

**FR-5: Code Analysis**

- Input: Code submissions, test cases
- Process: Execute code, analyze quality, detect AI patterns
- Output: Analysis report generated
- Error: Execution timeout, analysis service unavailable

**FR-6: Report Generation**

- Input: Assessment ID, analysis results
- Process: Aggregate data, generate visualizations, create report
- Output: Comprehensive assessment report
- Error: Insufficient data, report generation failure

## 9. Sequence Diagram

_[Sequence diagrams would be inserted here showing the interaction flow for key scenarios]_

**Assessment Creation Sequence:**

1. Interviewer → Frontend: Create Assessment Request
2. Frontend → Backend: POST /api/assessments
3. Backend → Database: Save Assessment
4. Database → Backend: Confirmation
5. Backend → Frontend: Assessment Created
6. Frontend → Interviewer: Success Message

**Assessment Taking Sequence:**

1. Candidate → Frontend: Access Assessment Link
2. Frontend → Backend: GET /api/assessments/{id}
3. Backend → Database: Retrieve Assessment
4. Database → Backend: Assessment Data
5. Backend → Frontend: Assessment Details
6. Frontend → Candidate: Display Assessment
7. Candidate → Frontend: Submit Responses
8. Frontend → Backend: POST /api/answers
9. Backend → Analysis Server: Trigger Analysis
10. Analysis Server → Backend: Analysis Complete
11. Backend → Frontend: Submission Confirmed

## 10. Class Diagrams

### 10.1 Initial Class Diagram

_[Initial class diagram would be inserted here showing basic entity relationships]_

**Core Classes:**

- User
- Organization
- Job
- Assessment
- Question
- Candidate
- Submission

### 10.2 Modified Class Diagram

_[Modified class diagram would be inserted here showing refined relationships and attributes]_

**Enhanced Classes with Attributes:**

- User (id, email, password, roles, createdAt)
- Organization (id, name, description, ownerId, settings)
- Assessment (id, title, description, timeLimit, questions)
- Question (id, type, content, options, correctAnswer)
- Analysis (id, submissionId, scores, feedback, report)

### 10.3 Detailed Class Diagram

_[Detailed class diagram would be inserted here showing complete implementation details]_

**Complete Class Structure:**

- All attributes with data types
- Method signatures
- Inheritance relationships
- Interface implementations
- Design patterns used

---

**CONCLUSION**

This Software Requirements Specification document provides a comprehensive overview of the Skillify.io Assessment Management Platform. The system addresses the modern needs of technical assessment and recruitment through innovative features including AI-powered code analysis, secure execution environments, and comprehensive reporting capabilities.

The platform's three-component architecture (Frontend, Backend, Analysis Server) ensures scalability, maintainability, and security while providing an excellent user experience for all stakeholders. The detailed requirements specified in this document serve as the foundation for the development and implementation of this graduation project.

---

**Project Information:**

- **Student Name:** [Your Name]
- **Student ID:** [Your Student ID]
- **Institution:** Computer Science Department, Menoufia University
- **Academic Year:** 2024-2025
- **Supervisor:** [Supervisor Name]
- **Submission Date:** [Date]

---

_This document represents the Software Requirements Specification for the Skillify.io Assessment Management Platform developed as a graduation project for the Computer Science program at Menoufia University, Egypt._




**JWT-Based Authentication**:

- Stateless authentication using JSON Web Tokens
- HttpOnly secure cookies for token storage
- Refresh token mechanism for session management
- Password encryption using BCrypt

**Authorization System**:

- Method-level security with @PreAuthorize annotations
- Custom permission evaluators for organization-specific access
- Role-based access control (RBAC)
- Resource-level permissions

### 5.2 API Architecture

**RESTful Design**:

- Standard HTTP methods (GET, POST, PUT, PATCH, DELETE)
- Consistent response format using ResponseDto wrapper
- Comprehensive error handling with GlobalExceptionHandler
- Input validation using Bean Validation annotations

**Response Format**:

```json
{
 "status": "OK",
 "success": true,
 "data": {
  /* actual payload */
 },
 "error": null,
 "timestamp": "2025-06-19T10:00:00.123456"
}
```

### 5.3 Database Design

**Entity Relationships**:

- User ↔ Organization (Many-to-Many through OrgMember)
- Organization → Job (One-to-Many)
- Job → Test (One-to-Many)
- Test → Question (One-to-Many)
- Test → TestAssignment (One-to-Many)
- TestAssignment → Answer (One-to-Many)

**Auditing**:

- Automatic tracking of created/updated timestamps
- User tracking for all modifications
- Soft delete capabilities for data retention

---

## 6. Frontend Application

### 6.1 Application Structure

**Route Organization**:

```
app/
├── (pages)/                    # Public pages
│   ├── about/                  # About us page
│   ├── solutions/              # Solutions showcase
│   ├── pricing/                # Pricing information
│   └── industries/             # Industry-specific content
├── (protected)/                # Authenticated routes
│   ├── dashboard/              # Main dashboard
│   │   ├── organization/       # Organization management
│   │   │   └── [org_id]/       # Specific organization
│   │   │       ├── job/        # Job management
│   │   │       │   └── [job_id]/
│   │   │       │       └── assessments/
│   │   │       │           └── [assessment_id]/
│   │   │       │               ├── add-questions/
│   │   │       │               ├── analytics/
│   │   │       │               ├── code/
│   │   │       │               └── send-invitation/
│   │   └── (personal)/         # Personal dashboard
│   └── assessments/            # Candidate assessment interface
│       └── [assessment_id]/    # Specific assessment
└── api/                        # API routes
```

### 6.2 Key Components

**Dashboard Features**:

- Organization overview and statistics
- Job management interface
- Assessment creation and editing
- Analytics and reporting dashboards
- User management and invitations

**Assessment Interface**:

- Professional coding environment
- Real-time code execution
- Progress tracking
- Auto-save functionality
- Responsive design for various devices

### 6.3 State Management

**Zustand Implementation**:

- Lightweight state management
- Persistent storage for user preferences
- Assessment progress tracking
- Real-time updates for collaborative features

---

## 7. Analysis Server

### 7.1 Analysis Pipeline

**Multi-Dimensional Analysis**:

1. **Correctness Analysis**: Code execution against test cases
2. **Code Quality Analysis**: Complexity and maintainability metrics
3. **AI Detection**: Pattern recognition for AI-generated code
4. **Style Analysis**: Coding convention adherence
5. **Performance Analysis**: Time and space complexity estimation
6. **Naming Convention Analysis**: Variable and function naming quality

### 7.2 Code Execution Environment

**Docker-Based Execution**:

- Secure, isolated code execution
- Support for multiple programming languages:
  - Python
  - JavaScript
  - Java
  - Go
  - Ruby
  - C++
- Automatic test case generation and validation
- Resource limitation and timeout handling

### 7.3 Reporting System

**Report Types**:

- **Individual Reports**: Detailed candidate performance analysis
- **Comparative Reports**: Cross-candidate comparison
- **Assessment Summary**: Overall test statistics
- **Trend Analysis**: Performance patterns over time

**Report Features**:

- Visual charts and graphs
- Detailed breakdowns by analysis dimension
- Ranking and scoring systems
- Export capabilities (PDF, Excel)

---

## 8. Database Design

### 8.1 PostgreSQL Schema

**Core Entities**:

- **Users**: Authentication and profile information
- **Organizations**: Company/entity management
- **Jobs**: Job posting details
- **Tests**: Assessment configurations
- **Questions**: Individual assessment items
- **TestAssignments**: Candidate-test relationships
- **Answers**: Candidate responses

**Relationship Mapping**:

- Proper foreign key constraints
- Cascade operations for data integrity
- Indexing for performance optimization
- Audit trails for all modifications

### 8.2 MongoDB Collections

**Analysis Data**:

- **assessments**: Test configurations and metadata
- **solutions**: Candidate code submissions
- **analysis_results**: Detailed analysis outcomes
- **reports**: Generated assessment reports
- **analysis_jobs**: Background processing status

---

## 9. Security Implementation

### 9.1 Authentication Security

- **Password Security**: BCrypt hashing with salt
- **JWT Security**: Signed tokens with expiration
- **Session Management**: Secure HttpOnly cookies
- **CORS Configuration**: Restricted cross-origin requests

### 9.2 Authorization Framework

- **Role-Based Access**: Hierarchical permission system
- **Resource-Level Security**: Fine-grained access control
- **Organization Isolation**: Data segregation by organization
- **API Security**: Method-level authorization checks

### 9.3 Data Protection

- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Output encoding and sanitization
- **CSRF Protection**: Token-based request validation

---

## 10. API Documentation

### 10.1 Swagger Integration

**Interactive Documentation**:

- Complete API endpoint documentation
- Request/response examples
- Authentication requirements
- Error code explanations
- Try-it-out functionality

**Access Points**:

- Backend API: `http://localhost:8080/swagger-ui/index.html`
- Analysis API: `http://localhost:5000/api/docs/`

### 10.2 API Categories

**Authentication APIs**:

- User registration and login
- Token refresh and logout
- Credential validation

**Organization Management APIs**:

- Organization CRUD operations
- Member management
- Permission handling

**Assessment APIs**:

- Test creation and management
- Question management
- Assignment handling
- Answer submission

**Analysis APIs**:

- Solution analysis
- Report generation
- Job monitoring

---

## 11. User Interface

### 11.1 Design Principles

**Modern UI/UX**:

- Clean, minimalist design
- Consistent component library (Shadcn UI)
- Responsive design for all devices
- Accessibility compliance (WCAG guidelines)
- Dark/light theme support

### 11.2 Key Interface Features

**Dashboard**:

- Comprehensive overview widgets
- Real-time statistics and charts
- Quick action buttons
- Navigation breadcrumbs

**Assessment Creation**:

- Drag-and-drop question builder
- Live preview functionality
- Batch operations support
- Template management

**Candidate Experience**:

- Professional coding environment
- Split-pane layout for problem/solution
- Real-time test execution
- Progress indicators

---

## 12. Testing & Quality Assurance

### 12.1 Testing Strategy

**Backend Testing**:

- Unit tests for service layer
- Integration tests for API endpoints
- Security testing for authentication
- Performance testing for scalability

**Frontend Testing**:

- Component unit tests
- End-to-end testing with Playwright
- Accessibility testing
- Cross-browser compatibility

**Analysis Server Testing**:

- Algorithm accuracy testing
- Performance benchmarking
- Docker environment testing
- Error handling validation

### 12.2 Quality Metrics

**Code Quality**:

- Code coverage reports
- Static code analysis
- Dependency vulnerability scanning
- Performance monitoring

---

## 13. Deployment & DevOps

### 13.1 Development Environment

**Local Setup**:

- Docker Compose for service orchestration
- Environment-specific configurations
- Hot reload for development
- Integrated debugging support

### 13.2 Production Considerations

**Scalability**:

- Microservices architecture
- Database connection pooling
- Caching strategies
- Load balancing capabilities

**Monitoring**:

- Application performance monitoring
- Error tracking and logging
- Health check endpoints
- Metrics collection

---

## 14. Future Enhancements

### 14.1 Planned Features

**Advanced Analytics**:

- Machine learning-based candidate scoring
- Predictive performance modeling
- Advanced visualization dashboards
- Custom report builders

**Integration Capabilities**:

- Third-party ATS integration
- Video interview integration
- Calendar scheduling
- Email automation

**Enhanced Assessment Types**:

- System design questions
- Database query challenges
- Architecture assessment
- Collaborative coding sessions

### 14.2 Technical Improvements

**Performance Optimization**:

- Database query optimization
- Caching layer implementation
- CDN integration
- Image optimization

**Security Enhancements**:

- Multi-factor authentication
- Advanced threat detection
- Audit logging
- Compliance certifications

---

## 15. Conclusion

### 15.1 Project Achievements

Skillify.io successfully addresses the challenges of modern technical assessment through:

1. **Comprehensive Platform**: End-to-end solution for assessment management
2. **Advanced Analysis**: AI-powered code evaluation and quality assessment
3. **User Experience**: Intuitive interfaces for both administrators and candidates
4. **Scalable Architecture**: Microservices design for enterprise-scale deployment
5. **Security Focus**: Enterprise-grade security implementation

### 15.2 Technical Innovation

The project demonstrates several innovative approaches:

- **Multi-dimensional Code Analysis**: Beyond simple correctness checking
- **AI Detection Algorithms**: Identifying AI-generated code patterns
- **Real-time Collaboration**: Seamless team-based evaluation
- **Flexible Assessment Framework**: Supporting various question types and formats

### 15.3 Learning Outcomes

This graduation project provided valuable experience in:

- **Full-Stack Development**: Modern web application architecture
- **Microservices Design**: Distributed system implementation
- **Database Design**: Relational and document database modeling
- **Security Implementation**: Authentication and authorization systems
- **API Design**: RESTful service architecture
- **DevOps Practices**: Containerization and deployment strategies

### 15.4 Impact and Applications

Skillify.io has potential applications in:

- **Corporate Hiring**: Technical interview automation
- **Educational Assessment**: Programming course evaluation
- **Certification Programs**: Skill validation and certification
- **Training Platforms**: Progress tracking and assessment

The platform represents a significant step forward in automated technical assessment, combining modern web technologies with advanced analysis capabilities to create a comprehensive solution for talent evaluation.

---

**Project Team**: [Your Name]
**Institution**: Computer Science Department, Menoufia University
**Academic Year**: 2024-2025
**Supervisor**: [Supervisor Name]

---

_This documentation represents the comprehensive technical and functional overview of the Skillify.io assessment management platform developed as a graduation project for the Computer Science program at Menoufia University, Egypt._
